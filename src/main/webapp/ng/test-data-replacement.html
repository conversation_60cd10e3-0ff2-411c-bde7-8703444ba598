<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据替换</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试API数据替换</h1>
        <p>这个页面用于测试拦截器是否真正替换了API返回的数据</p>
        
        <button class="button success" onclick="testReplacement()">🔍 测试数据替换</button>
        <button class="button" onclick="testWithoutInterceptor()">❌ 禁用拦截器测试</button>
        <button class="button" onclick="testWithInterceptor()">✅ 启用拦截器测试</button>
        <button class="button danger" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <!-- 引入拦截器 -->
    <script src="js/filter.js"></script>

    <script>
        function showResult(message, type = 'info', data = null) {
            const resultsDiv = document.getElementById('results');
            const resultHtml = `
                <div class="status ${type}">
                    ${message}
                </div>
                ${data ? `<div class="result">${typeof data === 'string' ? data : JSON.stringify(data, null, 2)}</div>` : ''}
            `;
            resultsDiv.innerHTML += resultHtml;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 测试数据替换
        function testReplacement() {
            clearResults();
            showResult('🧪 开始测试数据替换...', 'info');

            // 确保拦截器启用
            if (window.APIInterceptor) {
                window.APIInterceptor.toggle(true);
                showResult('✅ 拦截器已启用', 'success');
            }

            // 发送XMLHttpRequest
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '../ngres/xueshengguanli/student/list');
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        
                        // 检查拦截标记
                        const hasInterceptedFlag = data.intercepted === true;
                        const hasUniversalFlag = data.universalIntercepted === true;
                        const hasDebugInfo = !!data._debugInfo;
                        const hasNote = !!data.note;
                        
                        if (hasInterceptedFlag || hasUniversalFlag || hasDebugInfo || hasNote) {
                            showResult('🎉 数据替换成功！检测到拦截器修改的字段:', 'success');
                            
                            const interceptedFields = [];
                            if (hasInterceptedFlag) interceptedFields.push('intercepted: true');
                            if (hasUniversalFlag) interceptedFields.push('universalIntercepted: true');
                            if (hasDebugInfo) interceptedFields.push('_debugInfo: 存在');
                            if (hasNote) interceptedFields.push('note: ' + data.note);
                            
                            showResult('检测到的拦截字段:\n' + interceptedFields.join('\n'), 'info');
                            
                            // 检查学生数据
                            if (data.list && data.list.length > 0) {
                                const firstStudent = data.list[0];
                                if (firstStudent.intercepted || firstStudent.序号 || firstStudent.备注) {
                                    showResult('✅ 学生数据也被成功修改', 'success');
                                }
                            }
                            
                        } else {
                            showResult('❌ 数据替换失败！未检测到拦截器添加的字段', 'error');
                            showResult('这可能意味着拦截器没有正确工作，或者数据没有被替换', 'error');
                        }
                        
                        // 显示完整数据（截取前500字符）
                        const dataStr = JSON.stringify(data, null, 2);
                        showResult('API返回数据（前500字符）:', 'info', dataStr.substring(0, 500) + (dataStr.length > 500 ? '...' : ''));
                        
                    } catch (e) {
                        showResult('❌ 数据解析失败: ' + e.message, 'error');
                        showResult('原始响应:', 'info', xhr.responseText.substring(0, 500));
                    }
                } else {
                    showResult(`❌ 请求失败: ${xhr.status} ${xhr.statusText}`, 'error');
                }
            };

            xhr.onerror = function() {
                showResult('❌ 网络错误', 'error');
            };

            // 发送请求
            xhr.send(JSON.stringify({
                page: { currentPage: 1, itemsperpage: 10 },
                searchItems: {}
            }));
        }

        // 禁用拦截器测试
        function testWithoutInterceptor() {
            clearResults();
            showResult('❌ 禁用拦截器进行测试...', 'info');

            if (window.APIInterceptor) {
                window.APIInterceptor.toggle(false);
                showResult('❌ 拦截器已禁用', 'info');
            }

            // 发送请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '../ngres/xueshengguanli/student/list');
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        
                        // 检查是否有拦截标记
                        const hasInterceptedFlag = data.intercepted === true;
                        const hasUniversalFlag = data.universalIntercepted === true;
                        
                        if (!hasInterceptedFlag && !hasUniversalFlag) {
                            showResult('✅ 确认：禁用拦截器后数据未被修改', 'success');
                        } else {
                            showResult('⚠️ 警告：即使禁用拦截器，数据仍然被修改了', 'error');
                        }
                        
                        showResult('原始数据结构:', 'info', {
                            hasPage: !!data.page,
                            hasList: !!data.list,
                            listLength: data.list ? data.list.length : 0,
                            hasIntercepted: !!data.intercepted,
                            hasUniversalIntercepted: !!data.universalIntercepted
                        });
                        
                    } catch (e) {
                        showResult('❌ 数据解析失败: ' + e.message, 'error');
                    }
                } else {
                    showResult(`❌ 请求失败: ${xhr.status}`, 'error');
                }
                
                // 重新启用拦截器
                if (window.APIInterceptor) {
                    window.APIInterceptor.toggle(true);
                }
            };

            xhr.send(JSON.stringify({
                page: { currentPage: 1, itemsperpage: 10 },
                searchItems: {}
            }));
        }

        // 启用拦截器测试
        function testWithInterceptor() {
            clearResults();
            showResult('✅ 启用拦截器进行测试...', 'info');

            if (window.APIInterceptor) {
                window.APIInterceptor.toggle(true);
                showResult('✅ 拦截器已启用', 'success');
                
                // 显示当前规则
                const enabledRules = window.APIInterceptor.rules.filter(r => r.enabled);
                showResult(`当前启用的规则数量: ${enabledRules.length}`, 'info');
                enabledRules.forEach(rule => {
                    showResult(`- ${rule.name} (匹配: ${rule.match})`, 'info');
                });
            }

            // 发送请求
            testReplacement();
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 页面已加载，可以开始测试', 'info');
            showResult('建议测试顺序：\n1. 先测试"禁用拦截器"看原始数据\n2. 再测试"启用拦截器"看修改后数据\n3. 对比两次结果', 'info');
        });
    </script>
</body>
</html>
