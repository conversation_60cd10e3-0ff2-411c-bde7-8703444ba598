@charset "utf-8";
/* CSS Document */
/***********************golbal***********************/
span.required {
    color: #e02222;
    font-size: 12px;
    padding-left: 2px;
}

body{
	padding:0;
	margin:0;
	width:100%;
	height:auto;

	font-size:12px;
	color:#333333;
	background:url(../../../images/bg.png) no-repeat top center fixed;
}
.clear{
	clear:both;
}
img{
	border:0;
}
a{
	text-decoration:none;
}
a:hover{
	text-decoration:none;
}

/***********************stoptop***********************/

#stoptop{
	width:100%;
	height:auto;
	position:fixed;
	top:0;
	background:#fff;
	z-index:1;
}
#stoptop_kong{
	height:65px;/*modify by wang*/
}
#stoptop_kong_01{
    height:105px;
}

/***********************search***********************/
#search{
    width:98%;
    height:auto;
    margin:0 auto;
    padding:10px 0 0 0;
    position:relative;
}
#search_left{
    width:107px;
    height:auto;
    float:left;
}
#search_mid{
    float:left;
    margin:0 0 0 15px;
}
#search_mid_left{
    float:left;
}
#search_mid_left_01{
    float:left;
}
#autoCom{
    width:362px;
    height:30px;
    line-height:28px;
    border:1px solid #5693c1;
    padding:0 5px;
    font-family:"Microsoft Yahei";
    /*color:#bebebe;*/
    -moz-border-radius:3px 0 0 3px;
    -webkit-border-radius:3px 0 0 3px;
    border-radius:3px 0 0 3px;
}
#search_mid_left_02{
    float:left;
}
#search_mid_right{
    float:left;
    margin-left:2px;
}
.search_mid_right_title{
    color:#fff;
    width:100px;
    height:30px;
    line-height:30px;
    position:relative;
    cursor:pointer;
    background:#1968a0;
    -webkit-border-radius:0 3px 3px 0;
    -moz-border-radius:0 3px 3px 0;
    border-radius:0 3px 3px 0;
}
.search_mid_right_title_01{
    padding-left:8px;
}
.search_mid_right_title_02{
    padding-left:3px;
}
.search_mid_right_title_03{
    padding-left:3px;
}
.search_mid_right_neirong{
    border:1px solid #cccccc;
    background:#fff;
    overflow:hidden;
    display:none;
    padding:5px 50px 5px 20px;
    position:absolute;
    z-index:10;
    height:auto;
    border:1px solid rgba(0, 0, 0, 0.2);
    -webkit-border-radius:3px;
    -moz-border-radius:3px;
    border-radius:3px;
    -webkit-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
    -moz-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
    -webkit-background-clip:padding-box;
    -moz-background-clip:padding-box;
    background-clip:padding-box;
}
.search_mid_right_title:hover + .search_mid_right_neirong{
    display:block;
}
.search_mid_right_neirong:hover{
    display:block;
}
.search_mid_right_neirong ul{
    padding:0;
    margin:0;
    list-style-type:none;
}
.search_mid_right_neirong li{
    height:26px;
    line-height:26px;
}
.search_mid_right_neirong li a{
    color:#333333;
}
.search_mid_right_neirong li a:hover{
    color:#1968a0;
    font-weight:bold;
}
#search_right{
    float:right;
    font-size:14px;
    color:#005a9f;
    margin-top:0;
}
#search_right_zxkf{
    float:left;
}
#search_right_zxkf_01{
    float:left;
}
#search_right_zxkf_02{
    float:left;
}
#search_right_dhkf{
    float:left;
    margin-left:40px;
}

/*mydiv*/
.mydiv {
	z-index:999;
	width: 370px;
	height:auto;
	left:29.5%;/*modify by wangyl*/
	top:16.0%;/*modify by wangyl*/
	margin-left:-265px!important;/*FF IE7 该值为本身宽的一半 */
	margin-top:-60px!important;/*FF IE7 该值为本身高的一半*/
	margin-top:0px;
	position:fixed!important;/* FF IE7*/
	position:absolute;/*IE6*/
	_top:       expression(eval(document.compatMode &&
				document.compatMode=='CSS1Compat') ?
				documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :/*IE6*/
				document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);/*IE5 IE5.5*/

}
.bg,.popIframe {
	display:none;
	width: 100%;
	height: 100%;
	left:0;
	top:0;/*FF IE7*/
	filter:alpha(opacity=50);/*IE*/
	opacity:0.5;/*FF*/
	z-index:1;
	position:fixed!important;/*FF IE7*/
	position:absolute;/*IE6*/
	_top:       expression(eval(document.compatMode &&
				document.compatMode=='CSS1Compat') ?
				documentElement.scrollTop + (document.documentElement.clientHeight-this.offsetHeight)/2 :/*IE6*/
				document.body.scrollTop + (document.body.clientHeight - this.clientHeight)/2);
}
.popIframe {
	filter:alpha(opacity=0);/*IE*/
	opacity:0;/*FF*/
}
#blank_denglu{
	margin:10px 0 0 10px;
}
#blank_denglu a{
	font-size:16px;
	color:#000;
	text-decoration:none;
}
#blank_denglu a:hover{
	text-decoration:underline;
}
#search_jg{
	width:370px;
	height:auto;
	font-size:14px;
	margin:0 auto;
	background:#fff;
	border:1px solid #d7d7d7;
	-webkit-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
}
#search_jg_title{
	width:370px;
	line-height:30px;
	background:#1968a0;
}
#search_jg_title_left{
	width:90%;
	float:left;
	text-align:center;
	color:#fff;
	font-size:16px;
}
#search_jg_title_right{
	float:right;
	margin-right:10px;
}
#search_jg_neirong{
	width:94%;
	height:auto;
	margin:0 auto;
}
#search_jg_neirong ul{
	padding:0;
	margin:0 0 5px 0;
	list-style-type:none;
}
#search_jg_neirong ul li{
	border-bottom:1px solid #d7d7d7;
	padding:4px 0;
}
.search_jg_neirong_pink{
	color:#fe1ba5;
}
.search_jg_neirong_link{
	padding-top:3px;
	color:#666666;
}
.search_jg_neirong_link a{
	text-decoration:underline;
	color:#666666;
}
.search_jg_neirong_link a:hover{
	color:#fe1ba5;
}

/***********************header***********************/
#header_bg{
	width:100%;
	height:auto;
}

.fond_size{

}
.header_menu{
	font-size: 14px;
}
.header_submenu{
	font-size: 12px;
}

/***********header_shang***********/
#header_shang{
	width:100%;
	height:44px;
	background:url(../../../images/header_01.jpg) repeat-x;
   /*background-color: #98493b;*/
}

.bgcolor{
    background-color: #eaeff2;
}
.navbox{
	height:44px;
	position:relative;
	z-index:9;
	margin:auto;
}
.nav{
	height:44px;
}
.nav ul{
	padding:0;
	margin:0;
	list-style-type:none;
}
.nav ul li{
	float:left;
	position:relative;
	padding:0 15px;
}
.nav ul li a{
}
.nav ul li a span{
	display:block;
	float:left;
	line-height:44px;
	color:#ffffff;
	cursor:pointer;
	text-align:center; 
}
.nav ul li a img{
	display:block;
	float:left;
	margin:19px 0 0 8px;
}
.mj_hover_menu{
	text-decoration:none;
	background:url(../../../images/header_02.jpg) repeat-x;
	height:44px;
}
.nav ul li.selected .submenu{
	display:block;
	
}
.nav ul li .submenu{
	display:none;
	position:absolute;
	top:44px;
	left:0;
	z-index: 9999;
}
.submenu_neirong_bg{
	clear:both;
	width:300px;
	height:auto;
	background:#fbfdff;
	padding:5px 0;
	overflow:hidden;
	border:1px solid rgba(0, 0, 0, 0.2);
	-webkit-border-radius:0 0 6px 6px;
	-moz-border-radius:0 0 6px 6px;
	border-radius:0 0 6px 6px;
	-webkit-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	-webkit-background-clip:padding-box;
	-moz-background-clip:padding-box;
	background-clip:padding-box;
}
.submenu_neirong{
	width:300px;
	height:auto;
	margin:0 auto;

}
.nav ul li .submenu .submenu_neirong_bg .submenu_neirong ul li{
	clear:both;
	width:300px;
	height:26px;
	line-height:26px;
	padding:0;
}
.nav ul li .submenu .submenu_neirong_bg .submenu_neirong ul li.submenu_neirong_line{
	width:300px;
	height:1px;
	border-bottom:1px solid #b1a9a9;
	padding:0;
	margin:6px 0;
}
.nav ul li .submenu .submenu_neirong_bg .submenu_neirong ul li a{
	display:block;
	width:300px;
	height:26px;
	color:#2b2a2a;
}
.nav ul li .submenu .submenu_neirong_bg .submenu_neirong ul li a span{
    display:block;
    width:300px;
    height:26px;
    color:#2b2a2a;
}
.nav ul li .submenu .submenu_neirong_bg .submenu_neirong ul li a span:hover{
    color:#fff;
    background:#357ea9;
}

.nav ul li .submenu .submenu_neirong_bg .submenu_neirong ul li a:hover{
	color:#fff;
	background:#357ea9;
}
.nav ul li.submenu_right_li{
	padding:0 5px;
}
.nav ul li a .submenu_right_img{
	display:block;
	float:left;
	margin:17px 6px 0 0;
}
.nav ul li.submenu_right_li #submenu_right_xm{
	margin-top:14px;
	color:#fff;
}
.nav ul li.submenu_right_li .submenu_right_tx{
	margin-top:3px;
	border:1px solid #fff;
}
.nav ul li.submenu_right_li .pic{
    height: 36px;
    width: 36px;
}
#nav_menu_left{
	float:left;
	margin-left:0;
}
#nav_menu_right{
	float:right;
	margin-right:0;
}

/***********header_xia***********/
.header_xia{
	/*clear:both;*/
	width:100%;
	height:21px;
	line-height:21px;
	background: #f1f3f9 url(/images/header_08.jpg) repeat-x;
}
.header_xia_neirong{
	margin-left:5%;
}
.header_xia_neirong a{
	padding:0 15px;
	color:#666666;
}
.header_xia_neirong a:hover{
	color:#1968a0;
	font-weight:bold;
}
.header_xia_neirong a.active {
    color: #000000;
    font-weight: 700;
}
/***********************znx***********************/
#znx{
	position:fixed;
	top:125px;
	right:0;
	margin-right:76px;
	width:300px;
	height:auto;
	z-index:0;
}
.znx_neirong{
	background:#fdf9e1;
	-webkit-border-radius:4px;
	-moz-border-radius:4px;
	border-radius:4px;
	-webkit-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow:0 5px 10px rgba(0, 0, 0, 0.2);
}
.znx_neirong ul{
	padding:0;
	margin:0;
	list-style-type:none;
}
.znx_neirong ul li{
	margin:3px 0;
}
.znx_neirong_title{
	float:left;
}
.znx_neirong_link{
	float:left;
	margin-left:30px;
}
.znx_neirong_link a{
	color:#ea7350;
}
.znx_neirong_link a:hover{
	color:#1868a1;
}
/*add by wang start*/
.container-center{
    width:100%;
    height:auto;
    border:1px solid #e8e8e8;
    /*padding:15px 0;*/
}
.left-margin{
    margin: 0 0 0 15px;
}
.thumb100 {
    height: 100px;
    width: 100px;
}
.thumb50 {
    height: 50px;
    width: 50px;
    margin:0;
}
/*add by wang*/
.alertMsg{
    width: 100%;
    margin-top: 10px;
}
.container_top15{
    margin-top: 15px;
}
.ztree li span.button.pIconDept_ico_open{margin-right:2px; background: url(../../../images/folder-opened.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.pIconDept_ico_close{margin-right:2px; background: url(../../../images/folder-closed.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.pIconPost_ico_docu{margin-right:2px; background: url(../../../images/user_icon.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.pIconDept_ico_docu{margin-right:2px; background: url(../../../images/folder-closed.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.pIconPost_ico_open{margin-right:2px; background: url(../../../images/user_icon.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.pIconPost_ico_close{margin-right:2px; background: url(../../../images/user_icon.gif) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
/*add by wang end*/

.input-group .form-control {
	position: static;
}



