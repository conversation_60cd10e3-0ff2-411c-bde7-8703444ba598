/* http://keith-wood.name/countdown.html
 * Bosnian Latin initialisation for the jQuery countdown extension
 * Written by <PERSON>lem Me<PERSON> <EMAIL> (2011) */
(function($) {
	$.countdown.regional['bs'] = {
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		labels1: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', 'Sat', '<PERSON><PERSON>', '<PERSON>ku<PERSON>'],
		labels2: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>d<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Minute', 'Sekunde'],
		compactLabels: ['g', 'm', 't', 'd'],
		whichLabels: function(amount) {
			return (amount == 1 ? 1 : (amount >= 2 && amount <= 4 ? 2 : 0));
		},
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regional['bs']);
})(jQuery);
