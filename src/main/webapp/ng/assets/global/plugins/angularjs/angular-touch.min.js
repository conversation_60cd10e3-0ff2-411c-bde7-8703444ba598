/**
 * @license AngularJS v1.2.26
 * (c) 2010-2014 Google, Inc. http://angularjs.org
 * License: MIT
 */
!function(n,e){"use strict";var t=e.module("ngTouch",[]);t.factory("$swipe",[function(){function n(n){var e=n.touches&&n.touches.length?n.touches:[n],t=n.changedTouches&&n.changedTouches[0]||n.originalEvent&&n.originalEvent.changedTouches&&n.originalEvent.changedTouches[0]||e[0].originalEvent||e[0];return{x:t.clientX,y:t.clientY}}var e=10;return{bind:function(t,o){var c,i,u,a,r=!1;t.on("touchstart mousedown",function(e){u=n(e),r=!0,c=0,i=0,a=u,o.start&&o.start(u,e)}),t.on("touchcancel",function(n){r=!1,o.cancel&&o.cancel(n)}),t.on("touchmove mousemove",function(t){if(r&&u){var s=n(t);if(c+=Math.abs(s.x-a.x),i+=Math.abs(s.y-a.y),a=s,!(e>c&&e>i))return i>c?(r=!1,void(o.cancel&&o.cancel(t))):(t.preventDefault(),void(o.move&&o.move(s,t)))}}),t.on("touchend mouseup",function(e){r&&(r=!1,o.end&&o.end(n(e),e))})}}}]),t.config(["$provide",function(n){n.decorator("ngClickDirective",["$delegate",function(n){return n.shift(),n}])}]),t.directive("ngClick",["$parse","$timeout","$rootElement",function(n,t,o){function c(n,e,t,o){return Math.abs(n-t)<g&&Math.abs(e-o)<g}function i(n,e,t){for(var o=0;o<n.length;o+=2)if(c(n[o],n[o+1],e,t))return n.splice(o,o+2),!0;return!1}function u(n){if(!(Date.now()-s>v)){var e=n.touches&&n.touches.length?n.touches:[n],t=e[0].clientX,o=e[0].clientY;1>t&&1>o||h&&h[0]===t&&h[1]===o||(h&&(h=null),"label"===n.target.tagName.toLowerCase()&&(h=[t,o]),i(l,t,o)||(n.stopPropagation(),n.preventDefault(),n.target&&n.target.blur()))}}function a(n){var e=n.touches&&n.touches.length?n.touches:[n],o=e[0].clientX,c=e[0].clientY;l.push(o,c),t(function(){for(var n=0;n<l.length;n+=2)if(l[n]==o&&l[n+1]==c)return void l.splice(n,n+2)},v,!1)}function r(n,e){l||(o[0].addEventListener("click",u,!0),o[0].addEventListener("touchstart",a,!0),l=[]),s=Date.now(),i(l,n,e)}var s,l,h,f=750,d=12,v=2500,g=25,m="ng-click-active";return function(t,o,c){function i(){v=!1,o.removeClass(m)}var u,a,s,l,h=n(c.ngClick),v=!1;o.on("touchstart",function(n){v=!0,u=n.target?n.target:n.srcElement,3==u.nodeType&&(u=u.parentNode),o.addClass(m),a=Date.now();var e=n.touches&&n.touches.length?n.touches:[n],t=e[0].originalEvent||e[0];s=t.clientX,l=t.clientY}),o.on("touchmove",function(){i()}),o.on("touchcancel",function(){i()}),o.on("touchend",function(n){var t=Date.now()-a,h=n.changedTouches&&n.changedTouches.length?n.changedTouches:n.touches&&n.touches.length?n.touches:[n],g=h[0].originalEvent||h[0],m=g.clientX,p=g.clientY,w=Math.sqrt(Math.pow(m-s,2)+Math.pow(p-l,2));v&&f>t&&d>w&&(r(m,p),u&&u.blur(),e.isDefined(c.disabled)&&c.disabled!==!1||o.triggerHandler("click",[n])),i()}),o.onclick=function(){},o.on("click",function(n,e){t.$apply(function(){h(t,{$event:e||n})})}),o.on("mousedown",function(){o.addClass(m)}),o.on("mousemove mouseup",function(){o.removeClass(m)})}}])}(window,window.angular);