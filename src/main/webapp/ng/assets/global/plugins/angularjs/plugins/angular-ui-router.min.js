/**
 * State-based routing for AngularJS
 * @version v0.2.11
 * @link http://angular-ui.github.com/
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */

/* commonjs package manager support (eg componentjs) */
"undefined"!=typeof module&&"undefined"!=typeof exports&&module.exports===exports&&(module.exports="ui.router"),function(e,t,r){"use strict";function n(e,t){return q(new(q(function(){},{prototype:e})),t)}function i(e){return D(arguments,function(t){t!==e&&D(t,function(t,r){e.hasOwnProperty(r)||(e[r]=t)})}),e}function o(e,t){var r=[];for(var n in e.path){if(e.path[n]!==t.path[n])break;r.push(e.path[n])}return r}function a(e){if(Object.keys)return Object.keys(e);var r=[];return t.forEach(e,function(e,t){r.push(t)}),r}function u(e,t){if(Array.prototype.indexOf)return e.indexOf(t,Number(arguments[2])||0);var r=e.length>>>0,n=Number(arguments[2])||0;for(n=0>n?Math.ceil(n):Math.floor(n),0>n&&(n+=r);r>n;n++)if(n in e&&e[n]===t)return n;return-1}function s(e,t,r,n){var i,s=o(r,n),l={},c=[];for(var f in s)if(s[f].params&&(i=a(s[f].params),i.length))for(var p in i)u(c,i[p])>=0||(c.push(i[p]),l[i[p]]=e[i[p]]);return q({},l,t)}function l(e,t,r){if(!r){r=[];for(var n in e)r.push(n)}for(var i=0;i<r.length;i++){var o=r[i];if(e[o]!=t[o])return!1}return!0}function c(e,t){var r={};return D(e,function(e){r[e]=t[e]}),r}function f(e,t){var n=1,o=2,a={},u=[],s=a,l=q(e.when(a),{$$promises:a,$$values:a});this.study=function(a){function c(e,r){if(v[r]!==o){if(h.push(r),v[r]===n)throw h.splice(0,h.indexOf(r)),new Error("Cyclic dependency: "+h.join(" -> "));if(v[r]=n,V(e))p.push(r,[function(){return t.get(e)}],u);else{var i=t.annotate(e);D(i,function(e){e!==r&&a.hasOwnProperty(e)&&c(a[e],e)}),p.push(r,e,i)}h.pop(),v[r]=o}}function f(e){return k(e)&&e.then&&e.$$promises}if(!k(a))throw new Error("'invocables' must be an object");var p=[],h=[],v={};return D(a,c),a=h=v=null,function(n,o,a){function u(){--g||(w||i(m,o.$$values),$.$$values=m,$.$$promises=!0,delete $.$$inheritedValues,v.resolve(m))}function c(e){$.$$failure=e,v.reject(e)}function h(r,i,o){function s(e){f.reject(e),c(e)}function l(){if(!A($.$$failure))try{f.resolve(t.invoke(i,a,m)),f.promise.then(function(e){m[r]=e,u()},s)}catch(e){s(e)}}var f=e.defer(),p=0;D(o,function(e){d.hasOwnProperty(e)&&!n.hasOwnProperty(e)&&(p++,d[e].then(function(t){m[e]=t,--p||l()},s))}),p||l(),d[r]=f.promise}if(f(n)&&a===r&&(a=o,o=n,n=null),n){if(!k(n))throw new Error("'locals' must be an object")}else n=s;if(o){if(!f(o))throw new Error("'parent' must be a promise returned by $resolve.resolve()")}else o=l;var v=e.defer(),$=v.promise,d=$.$$promises={},m=q({},n),g=1+p.length/3,w=!1;if(A(o.$$failure))return c(o.$$failure),$;o.$$inheritedValues&&i(m,o.$$inheritedValues),o.$$values?(w=i(m,o.$$values),$.$$inheritedValues=o.$$values,u()):(o.$$inheritedValues&&($.$$inheritedValues=o.$$inheritedValues),q(d,o.$$promises),o.then(u,c));for(var y=0,b=p.length;b>y;y+=3)n.hasOwnProperty(p[y])?u():h(p[y],p[y+1],p[y+2]);return $}},this.resolve=function(e,t,r,n){return this.study(e)(t,r,n)}}function p(e,t,r){this.fromConfig=function(e,t,r){return A(e.template)?this.fromString(e.template,t):A(e.templateUrl)?this.fromUrl(e.templateUrl,t):A(e.templateProvider)?this.fromProvider(e.templateProvider,t,r):null},this.fromString=function(e,t){return I(e)?e(t):e},this.fromUrl=function(r,n){return I(r)&&(r=r(n)),null==r?null:e.get(r,{cache:t}).then(function(e){return e.data})},this.fromProvider=function(e,t,n){return r.invoke(e,null,n||{params:t})}}function h(e,n){function i(e){return A(e)?this.type.decode(e):$.$$getDefaultValue(this)}function o(t,r,n){if(!/^\w+(-+\w+)*$/.test(t))throw new Error("Invalid parameter name '"+t+"' in pattern '"+e+"'");if(h[t])throw new Error("Duplicate parameter name '"+t+"' in pattern '"+e+"'");h[t]=q({type:r||new v,$value:i},n)}function a(e,t,r){var n=e.replace(/[\\\[\]\^$*+?.()|{}]/g,"\\$&");if(!t)return n;var i=r?"?":"";return n+i+"("+t+")"+i}function u(e){if(!n.params||!n.params[e])return{};var t=n.params[e];return k(t)?t:{value:t}}n=t.isObject(n)?n:{};var s,l=/([:*])(\w+)|\{(\w+)(?:\:((?:[^{}\\]+|\\.|\{(?:[^{}\\]+|\\.)*\})+))?\}/g,c="^",f=0,p=this.segments=[],h=this.params={};this.source=e;for(var d,m,g,w,y;(s=l.exec(e))&&(d=s[2]||s[3],m=s[4]||("*"==s[1]?".*":"[^/]*"),g=e.substring(f,s.index),w=this.$types[m]||new v({pattern:new RegExp(m)}),y=u(d),!(g.indexOf("?")>=0));)c+=a(g,w.$subPattern(),A(y.value)),o(d,w,y),p.push(g),f=l.lastIndex;g=e.substring(f);var b=g.indexOf("?");if(b>=0){var E=this.sourceSearch=g.substring(b);g=g.substring(0,b),this.sourcePath=e.substring(0,f+b),D(E.substring(1).split(/[&?]/),function(e){o(e,null,u(e))})}else this.sourcePath=e,this.sourceSearch="";c+=a(g)+(n.strict===!1?"/?":"")+"$",p.push(g),this.regexp=new RegExp(c,n.caseInsensitive?"i":r),this.prefix=p[0]}function v(e){q(this,e)}function $(){function e(){return{strict:o,caseInsensitive:i}}function t(e){return I(e)||M(e)&&I(e[e.length-1])}function r(){D(u,function(e){if(h.prototype.$types[e.name])throw new Error("A type named '"+e.name+"' has already been defined.");var r=new v(t(e.def)?n.invoke(e.def):e.def);h.prototype.$types[e.name]=r})}var n,i=!1,o=!0,a=!0,u=[],s={"int":{decode:function(e){return parseInt(e,10)},is:function(e){return A(e)?this.decode(e.toString())===e:!1},pattern:/\d+/},bool:{encode:function(e){return e?1:0},decode:function(e){return 0===parseInt(e,10)?!1:!0},is:function(e){return e===!0||e===!1},pattern:/0|1/},string:{pattern:/[^\/]*/},date:{equals:function(e,t){return e.toISOString()===t.toISOString()},decode:function(e){return new Date(e)},encode:function(e){return[e.getFullYear(),("0"+(e.getMonth()+1)).slice(-2),("0"+e.getDate()).slice(-2)].join("-")},pattern:/[0-9]{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[1-2][0-9]|3[0-1])/}};$.$$getDefaultValue=function(e){if(!t(e.value))return e.value;if(!n)throw new Error("Injectable functions cannot be called at configuration time");return n.invoke(e.value)},this.caseInsensitive=function(e){i=e},this.strictMode=function(e){o=e},this.compile=function(t,r){return new h(t,q(e(),r))},this.isMatcher=function(e){if(!k(e))return!1;var t=!0;return D(h.prototype,function(r,n){I(r)&&(t=t&&A(e[n])&&I(e[n]))}),t},this.type=function(e,t){return A(t)?(u.push({name:e,def:t}),a||r(),this):h.prototype.$types[e]},this.$get=["$injector",function(e){return n=e,a=!1,h.prototype.$types={},r(),D(s,function(e,t){h.prototype.$types[t]||(h.prototype.$types[t]=new v(e))}),this}]}function d(e,t){function n(e){var t=/^\^((?:\\[^a-zA-Z0-9]|[^\\\[\]\^$*+?.()|{}]+)*)/.exec(e.source);return null!=t?t[1].replace(/\\(.)/g,"$1"):""}function i(e,t){return e.replace(/\$(\$|\d{1,2})/,function(e,r){return t["$"===r?0:Number(r)]})}function o(e,t,r){if(!r)return!1;var n=e.invoke(t,t,{$match:r});return A(n)?n:!0}function a(t,r,n,i){function o(e,t,r){return"/"===p?e:t?p.slice(0,-1)+e:r?p.slice(1)+e:e}function a(e){function r(e){var r=e(n,t);return r?(V(r)&&t.replace().url(r),!0):!1}if(!e||!e.defaultPrevented){var i,o=s.length;for(i=0;o>i;i++)if(r(s[i]))return;l&&r(l)}}function f(){return u=u||r.$on("$locationChangeSuccess",a)}var p=i.baseHref(),h=t.url();return c||f(),{sync:function(){a()},listen:function(){return f()},update:function(e){return e?void(h=t.url()):void(t.url()!==h&&(t.url(h),t.replace()))},push:function(e,r,n){t.url(e.format(r||{})),n&&n.replace&&t.replace()},href:function(r,n,i){if(!r.validates(n))return null;var a=e.html5Mode(),u=r.format(n);if(i=i||{},a||null===u||(u="#"+e.hashPrefix()+u),u=o(u,a,i.absolute),!i.absolute||!u)return u;var s=!a&&u?"/":"",l=t.port();return l=80===l||443===l?"":":"+l,[t.protocol(),"://",t.host(),l,s,u].join("")}}}var u,s=[],l=null,c=!1;this.rule=function(e){if(!I(e))throw new Error("'rule' must be a function");return s.push(e),this},this.otherwise=function(e){if(V(e)){var t=e;e=function(){return t}}else if(!I(e))throw new Error("'rule' must be a function");return l=e,this},this.when=function(e,r){var a,u=V(r);if(V(e)&&(e=t.compile(e)),!u&&!I(r)&&!M(r))throw new Error("invalid 'handler' in when()");var s={matcher:function(e,r){return u&&(a=t.compile(r),r=["$match",function(e){return a.format(e)}]),q(function(t,n){return o(t,r,e.exec(n.path(),n.search()))},{prefix:V(e.prefix)?e.prefix:""})},regex:function(e,t){if(e.global||e.sticky)throw new Error("when() RegExp must not be global or sticky");return u&&(a=t,t=["$match",function(e){return i(a,e)}]),q(function(r,n){return o(r,t,e.exec(n.path()))},{prefix:n(e)})}},l={matcher:t.isMatcher(e),regex:e instanceof RegExp};for(var c in l)if(l[c])return this.rule(s[c](e,r));throw new Error("invalid 'what' in when()")},this.deferIntercept=function(e){e===r&&(e=!0),c=e},this.$get=a,a.$inject=["$location","$rootScope","$injector","$browser"]}function m(e,i){function o(e){return 0===e.indexOf(".")||0===e.indexOf("^")}function u(e,t){if(!e)return r;var n=V(e),i=n?e:e.name,a=o(i);if(a){if(!t)throw new Error("No reference point given for path '"+i+"'");for(var u=i.split("."),s=0,l=u.length,c=t;l>s;s++)if(""!==u[s]||0!==s){if("^"!==u[s])break;if(!c.parent)throw new Error("Path '"+i+"' not valid for state '"+t.name+"'");c=c.parent}else c=t;u=u.slice(s).join("."),i=c.name+(c.name&&u?".":"")+u}var f=b[i];return!f||!n&&(n||f!==e&&f.self!==e)?r:f}function f(e,t){E[e]||(E[e]=[]),E[e].push(t)}function p(t){t=n(t,{self:t,resolve:t.resolve||{},toString:function(){return this.name}});var r=t.name;if(!V(r)||r.indexOf("@")>=0)throw new Error("State must have a valid name");if(b.hasOwnProperty(r))throw new Error("State '"+r+"'' is already defined");var i=-1!==r.indexOf(".")?r.substring(0,r.lastIndexOf(".")):V(t.parent)?t.parent:"";if(i&&!b[i])return f(i,t.self);for(var o in S)I(S[o])&&(t[o]=S[o](t,S.$delegates[o]));if(b[r]=t,!t[x]&&t.url&&e.when(t.url,["$match","$stateParams",function(e,r){y.$current.navigable==t&&l(e,r)||y.transitionTo(t,e,{location:!1})}]),E[r])for(var a=0;a<E[r].length;a++)p(E[r][a]);return t}function h(e){return e.indexOf("*")>-1}function v(e){var t=e.split("."),r=y.$current.name.split(".");if("**"===t[0]&&(r=r.slice(r.indexOf(t[1])),r.unshift("**")),"**"===t[t.length-1]&&(r.splice(r.indexOf(t[t.length-2])+1,Number.MAX_VALUE),r.push("**")),t.length!=r.length)return!1;for(var n=0,i=t.length;i>n;n++)"*"===t[n]&&(r[n]="*");return r.join("")===t.join("")}function $(e,t){return V(e)&&!A(t)?S[e]:I(t)&&V(e)?(S[e]&&!S.$delegates[e]&&(S.$delegates[e]=S[e]),S[e]=t,this):this}function d(e,t){return k(e)?t=e:t.name=e,p(t),this}function m(e,i,o,f,p,$,d){function m(t,r,n,o){var a=e.$broadcast("$stateNotFound",t,r,n);if(a.defaultPrevented)return d.update(),P;if(!a.retry)return null;if(o.$retry)return d.update(),O;var u=y.transition=i.when(a.retry);return u.then(function(){return u!==y.transition?S:(t.options.$retry=!0,y.transitionTo(t.to,t.toParams,t.options))},function(){return P}),d.update(),u}function E(e,r,n,u,s){var l=n?r:c(a(e.params),r),h={$stateParams:l};s.resolve=p.resolve(e.resolve,h,s.resolve,e);var v=[s.resolve.then(function(e){s.globals=e})];return u&&v.push(u),D(e.views,function(r,n){var i=r.resolve&&r.resolve!==e.resolve?r.resolve:{};i.$template=[function(){return o.load(n,{view:r,locals:h,params:l})||""}],v.push(p.resolve(i,h,s.resolve,e).then(function(o){if(I(r.controllerProvider)||M(r.controllerProvider)){var a=t.extend({},i,h);o.$$controller=f.invoke(r.controllerProvider,null,a)}else o.$$controller=r.controller;o.$$state=e,o.$$controllerAs=r.controllerAs,s[n]=o}))}),i.all(v).then(function(){return s})}var S=i.reject(new Error("transition superseded")),j=i.reject(new Error("transition prevented")),P=i.reject(new Error("transition aborted")),O=i.reject(new Error("transition failed"));return w.locals={resolve:null,globals:{$stateParams:{}}},y={params:{},current:w.self,$current:w,transition:null},y.reload=function(){y.transitionTo(y.current,$,{reload:!0,inherit:!1,notify:!1})},y.go=function(e,t,r){return y.transitionTo(e,t,q({inherit:!0,relative:y.$current},r))},y.transitionTo=function(t,r,o){r=r||{},o=q({location:!0,inherit:!1,relative:null,notify:!0,reload:!1,$retry:!1},o||{});var p,h=y.$current,v=y.params,b=h.path,P=u(t,o.relative);if(!A(P)){var O={to:t,toParams:r,options:o},C=m(O,h.self,v,o);if(C)return C;if(t=O.to,r=O.toParams,o=O.options,P=u(t,o.relative),!A(P)){if(!o.relative)throw new Error("No such state '"+t+"'");throw new Error("Could not resolve '"+t+"' from state '"+o.relative+"'")}}if(P[x])throw new Error("Cannot transition to abstract state '"+t+"'");o.inherit&&(r=s($,r||{},y.$current,P)),t=P;var I=t.path,V=0,k=I[V],M=w.locals,D=[];if(!o.reload)for(;k&&k===b[V]&&l(r,v,k.ownParams);)M=D[V]=k.locals,V++,k=I[V];if(g(t,h,M,o))return t.self.reloadOnSearch!==!1&&d.update(),y.transition=null,i.when(y.current);if(r=c(a(t.params),r||{}),o.notify&&e.$broadcast("$stateChangeStart",t.self,r,h.self,v).defaultPrevented)return d.update(),j;for(var F=i.when(M),U=V;U<I.length;U++,k=I[U])M=D[U]=n(M),F=E(k,r,k===t,F,M);var N=y.transition=F.then(function(){var n,i,a;if(y.transition!==N)return S;for(n=b.length-1;n>=V;n--)a=b[n],a.self.onExit&&f.invoke(a.self.onExit,a.self,a.locals.globals),a.locals=null;for(n=V;n<I.length;n++)i=I[n],i.locals=D[n],i.self.onEnter&&f.invoke(i.self.onEnter,i.self,i.locals.globals);return y.transition!==N?S:(y.$current=t,y.current=t.self,y.params=r,R(y.params,$),y.transition=null,o.location&&t.navigable&&d.push(t.navigable.url,t.navigable.locals.globals.$stateParams,{replace:"replace"===o.location}),o.notify&&e.$broadcast("$stateChangeSuccess",t.self,r,h.self,v),d.update(!0),y.current)},function(n){return y.transition!==N?S:(y.transition=null,p=e.$broadcast("$stateChangeError",t.self,r,h.self,v,n),p.defaultPrevented||d.update(),i.reject(n))});return N},y.is=function(e,n){var i=u(e);return A(i)?y.$current!==i?!1:A(n)&&null!==n?t.equals($,n):!0:r},y.includes=function(e,t){if(V(e)&&h(e)){if(!v(e))return!1;e=y.$current.name}var n=u(e);return A(n)?A(y.$current.includes[n.name])?l(t,$):!1:r},y.href=function(e,t,r){r=q({lossy:!0,inherit:!0,absolute:!1,relative:y.$current},r||{});var n=u(e,r.relative);if(!A(n))return null;r.inherit&&(t=s($,t||{},y.$current,n));var i=n&&r.lossy?n.navigable:n;return i&&i.url?d.href(i.url,c(a(n.params),t||{}),{absolute:r.absolute}):null},y.get=function(e,t){if(0===arguments.length)return a(b).map(function(e){return b[e].self});var r=u(e,t);return r&&r.self?r.self:null},y}function g(e,t,r,n){return e!==t||(r!==t.locals||n.reload)&&e.self.reloadOnSearch!==!1?void 0:!0}var w,y,b={},E={},x="abstract",S={parent:function(e){if(A(e.parent)&&e.parent)return u(e.parent);var t=/^(.+)\.[^.]+$/.exec(e.name);return t?u(t[1]):w},data:function(e){return e.parent&&e.parent.data&&(e.data=e.self.data=q({},e.parent.data,e.data)),e.data},url:function(e){var t=e.url,r={params:e.params||{}};if(V(t))return"^"==t.charAt(0)?i.compile(t.substring(1),r):(e.parent.navigable||w).url.concat(t,r);if(!t||i.isMatcher(t))return t;throw new Error("Invalid url '"+t+"' in state '"+e+"'")},navigable:function(e){return e.url?e:e.parent?e.parent.navigable:null},params:function(e){return e.params?e.params:e.url?e.url.params:e.parent.params},views:function(e){var t={};return D(A(e.views)?e.views:{"":e},function(r,n){n.indexOf("@")<0&&(n+="@"+e.parent.name),t[n]=r}),t},ownParams:function(e){if(e.params=e.params||{},!e.parent)return a(e.params);var t={};D(e.params,function(e,r){t[r]=!0}),D(e.parent.params,function(r,n){if(!t[n])throw new Error("Missing required parameter '"+n+"' in state '"+e.name+"'");t[n]=!1});var r=[];return D(t,function(e,t){e&&r.push(t)}),r},path:function(e){return e.parent?e.parent.path.concat(e):[]},includes:function(e){var t=e.parent?q({},e.parent.includes):{};return t[e.name]=!0,t},$delegates:{}};w=p({name:"",url:"^",views:null,"abstract":!0}),w.navigable=null,this.decorator=$,this.state=d,this.$get=m,m.$inject=["$rootScope","$q","$view","$injector","$resolve","$stateParams","$urlRouter"]}function g(){function e(e,t){return{load:function(r,n){var i,o={template:null,controller:null,view:null,locals:null,notify:!0,async:!0,params:{}};return n=q(o,n),n.view&&(i=t.fromConfig(n.view,n.params,n.locals)),i&&n.notify&&e.$broadcast("$viewContentLoading",n),i}}}this.$get=e,e.$inject=["$rootScope","$templateFactory"]}function w(){var e=!1;this.useAnchorScroll=function(){e=!0},this.$get=["$anchorScroll","$timeout",function(t,r){return e?t:function(e){r(function(){e[0].scrollIntoView()},0,!1)}}]}function y(e,r,n){function i(){return r.has?function(e){return r.has(e)?r.get(e):null}:function(e){try{return r.get(e)}catch(t){return null}}}function o(e,t){var r=function(){return{enter:function(e,t,r){t.after(e),r()},leave:function(e,t){e.remove(),t()}}};if(s)return{enter:function(e,t,r){s.enter(e,null,t,r)},leave:function(e,t){s.leave(e,t)}};if(u){var n=u&&u(t,e);return{enter:function(e,t,r){n.enter(e,null,t),r()},leave:function(e,t){n.leave(e),t()}}}return r()}var a=i(),u=a("$animator"),s=a("$animate"),l={restrict:"ECA",terminal:!0,priority:400,transclude:"element",compile:function(r,i,a){return function(r,i,u){function s(){c&&(c.remove(),c=null),p&&(p.$destroy(),p=null),f&&(d.leave(f,function(){c=null}),c=f,f=null)}function l(o){var l,c=E(u,i.inheritedData("$uiView")),m=c&&e.$current&&e.$current.locals[c];if(o||m!==h){l=r.$new(),h=e.$current.locals[c];var g=a(l,function(e){d.enter(e,i,function(){(t.isDefined($)&&!$||r.$eval($))&&n(e)}),s()});f=g,p=l,p.$emit("$viewContentLoaded"),p.$eval(v)}}var c,f,p,h,v=u.onload||"",$=u.autoscroll,d=o(u,r);r.$on("$stateChangeSuccess",function(){l(!1)}),r.$on("$viewContentLoading",function(){l(!1)}),l(!0)}}};return l}function b(e,t,r){return{restrict:"ECA",priority:-400,compile:function(n){var i=n.html();return function(n,o,a){var u=r.$current,s=E(a,o.inheritedData("$uiView")),l=u&&u.locals[s];if(l){o.data("$uiView",{name:s,state:l.$$state}),o.html(l.$template?l.$template:i);var c=e(o.contents());if(l.$$controller){l.$scope=n;var f=t(l.$$controller,l);l.$$controllerAs&&(n[l.$$controllerAs]=f),o.data("$ngControllerController",f),o.children().data("$ngControllerController",f)}c(n)}}}}}function E(e,t){var r=e.uiView||e.name||"";return r.indexOf("@")>=0?r:r+"@"+(t?t.state.name:"")}function x(e,t){var r,n=e.match(/^\s*({[^}]*})\s*$/);if(n&&(e=t+"("+n[1]+")"),r=e.replace(/\n/g," ").match(/^([^(]+?)\s*(\((.*)\))?$/),!r||4!==r.length)throw new Error("Invalid state ref '"+e+"'");return{state:r[1],paramExpr:r[3]||null}}function S(e){var t=e.parent().inheritedData("$uiView");return t&&t.state&&t.state.name?t.state:void 0}function j(e,r){var n=["location","inherit","reload"];return{restrict:"A",require:["?^uiSrefActive","?^uiSrefActiveEq"],link:function(i,o,a,u){var s=x(a.uiSref,e.current.name),l=null,c=S(o)||e.$current,f="FORM"===o[0].nodeName,p=f?"action":"href",h=!0,v={relative:c,inherit:!0},$=i.$eval(a.uiSrefOpts)||{};t.forEach(n,function(e){e in $&&(v[e]=$[e])});var d=function(t){if(t&&(l=t),h){var r=e.href(s.state,l,v),n=u[1]||u[0];return n&&n.$$setStateInfo(s.state,l),null===r?(h=!1,!1):void(o[0][p]=r)}};s.paramExpr&&(i.$watch(s.paramExpr,function(e){e!==l&&d(e)},!0),l=i.$eval(s.paramExpr)),d(),f||o.bind("click",function(t){var n=t.which||t.button;if(!(n>1||t.ctrlKey||t.metaKey||t.shiftKey||o.attr("target"))){var i=r(function(){e.go(s.state,l,v)});t.preventDefault(),t.preventDefault=function(){r.cancel(i)}}})}}}function P(e,t,r){return{restrict:"A",controller:["$scope","$element","$attrs",function(n,i,o){function a(){u()?i.addClass(p):i.removeClass(p)}function u(){return"undefined"!=typeof o.uiSrefActiveEq?e.$current.self===c&&s():e.includes(c.name)&&s()}function s(){return!f||l(f,t)}var c,f,p;p=r(o.uiSrefActiveEq||o.uiSrefActive||"",!1)(n),this.$$setStateInfo=function(t,r){c=e.get(t,S(i)),f=r,a()},n.$on("$stateChangeSuccess",a)}]}}function O(e){return function(t){return e.is(t)}}function C(e){return function(t){return e.includes(t)}}var A=t.isDefined,I=t.isFunction,V=t.isString,k=t.isObject,M=t.isArray,D=t.forEach,q=t.extend,R=t.copy;t.module("ui.router.util",["ng"]),t.module("ui.router.router",["ui.router.util"]),t.module("ui.router.state",["ui.router.router","ui.router.util"]),t.module("ui.router",["ui.router.state"]),t.module("ui.router.compat",["ui.router"]),f.$inject=["$q","$injector"],t.module("ui.router.util").service("$resolve",f),p.$inject=["$http","$templateCache","$injector"],t.module("ui.router.util").service("$templateFactory",p),h.prototype.concat=function(e,t){return new h(this.sourcePath+e+this.sourceSearch,t)},h.prototype.toString=function(){return this.source},h.prototype.exec=function(e,t){var r=this.regexp.exec(e);if(!r)return null;t=t||{};var n,i,o,a=this.parameters(),u=a.length,s=this.segments.length-1,l={};if(s!==r.length-1)throw new Error("Unbalanced capture group in route '"+this.source+"'");for(n=0;s>n;n++)o=a[n],i=this.params[o],l[o]=i.$value(r[n+1]);for(;u>n;n++)o=a[n],i=this.params[o],l[o]=i.$value(t[o]);return l},h.prototype.parameters=function(e){return A(e)?this.params[e]||null:a(this.params)},h.prototype.validates=function(e){var t,r,n=!0,i=this;return D(e,function(e,o){i.params[o]&&(r=i.params[o],t=!e&&A(r.value),n=n&&(t||r.type.is(e)))}),n},h.prototype.format=function(e){var t=this.segments,r=this.parameters();if(!e)return t.join("").replace("//","/");var n,i,o,a,u,s,l=t.length-1,c=r.length,f=t[0];if(!this.validates(e))return null;for(n=0;l>n;n++)a=r[n],o=e[a],u=this.params[a],(A(o)||"/"!==t[n]&&"/"!==t[n+1])&&(null!=o&&(f+=encodeURIComponent(u.type.encode(o))),f+=t[n+1]);for(;c>n;n++)a=r[n],o=e[a],null!=o&&(s=M(o),s&&(o=o.map(encodeURIComponent).join("&"+a+"=")),f+=(i?"&":"?")+a+"="+(s?o:encodeURIComponent(o)),i=!0);return f},h.prototype.$types={},v.prototype.is=function(){return!0},v.prototype.encode=function(e){return e},v.prototype.decode=function(e){return e},v.prototype.equals=function(e,t){return e==t},v.prototype.$subPattern=function(){var e=this.pattern.toString();return e.substr(1,e.length-2)},v.prototype.pattern=/.*/,t.module("ui.router.util").provider("$urlMatcherFactory",$),d.$inject=["$locationProvider","$urlMatcherFactoryProvider"],t.module("ui.router.router").provider("$urlRouter",d),m.$inject=["$urlRouterProvider","$urlMatcherFactoryProvider"],t.module("ui.router.state").value("$stateParams",{}).provider("$state",m),g.$inject=[],t.module("ui.router.state").provider("$view",g),t.module("ui.router.state").provider("$uiViewScroll",w),y.$inject=["$state","$injector","$uiViewScroll"],b.$inject=["$compile","$controller","$state"],t.module("ui.router.state").directive("uiView",y),t.module("ui.router.state").directive("uiView",b),j.$inject=["$state","$timeout"],P.$inject=["$state","$stateParams","$interpolate"],t.module("ui.router.state").directive("uiSref",j).directive("uiSrefActive",P).directive("uiSrefActiveEq",P),O.$inject=["$state"],C.$inject=["$state"],t.module("ui.router.state").filter("isState",O).filter("includedByState",C)}(window,window.angular);