.amChartsDataSetSelector
{
    font-size:12px;
    font-family:verdana,helvetica,arial,sans-serif;
}

.amChartsPeriodSelector
{
    font-size:12px;
    font-family:verdana,helvetica,arial,sans-serif;
}

.amChartsButtonSelected
{
   background-color:#CC0000;
   border-style:solid;
   border-color:#CC0000;
   border-width:1px;
   color:#FFFFFF;
   -moz-border-radius: 5px;
   border-radius: 5px;
   margin: 1px;
   outline: none;
   box-sizing: border-box;
}

.amChartsButton
{
    color: #000000;
    background: transparent;
    opacity: 0.7;
    border: 1px solid rgba(0, 0, 0, .3);
   -moz-border-radius: 5px;
   border-radius: 5px;
   margin: 1px;
   outline: none;
   box-sizing: border-box;
}

.amChartsCompareList
{
   border-style:solid;
   border-color:#CCCCCC;
   border-width:1px;
}

.amChartsCompareList div
{
   -webkit-box-sizing: initial;
   box-sizing: initial;
}

.amChartsInputField
{

}

.amChartsLegend
{

}

.amChartsPanel
{

}