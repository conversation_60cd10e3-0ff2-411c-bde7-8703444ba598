.ammapAlert 
{
    display:table-cell; 
    vertical-align:middle; 
    text-align:center; 
    font-family:verdana,helvetica,arial,sans-serif;
    font-size:12px;
    color:#CC0000;
}

.ammapDescriptionWindow
{
   font-size:11px;
   font-family:verdana,helvetica,arial,sans-serif;    
   background-color:#FFFFFF;
   border-style:solid;
   border-color:#DADADA;
   border-width:1px;
   color:#000000;
   padding:8px;  
}

.ammapDescriptionTitle
{
   font-size:12px;
   font-weight:bold;
   font-family:verdana,helvetica,arial,sans-serif;
   padding-bottom:5px;    
}

.ammapDescriptionWindowCloseButton
{
    
}

.ammapObjectList ul 
{
    padding-left:20px; 
    list-style:square outside; 
    color:#999999;
    font-family:verdana,helvetica,arial,sans-serif;
    font-size: 12px;
}

.ammapObjectList ul ul 
{
    padding-left:14px;
}

.ammapObjectList a 
{
    color:#000000;
}

.ammapObjectList a 
{
    color:#000000;
    text-decoration:none;
    display:block;
    padding:2px;
}

.ammapObjectList a:hover 
{
    color:#CC0000;
    text-decoration:none;
    background:#FFFFFF;
    cursor:pointer;
    display:block;
}

.ammapDescriptionText 
{
	overflow: auto; 
}


.amChartsPlotArea
{
	
}