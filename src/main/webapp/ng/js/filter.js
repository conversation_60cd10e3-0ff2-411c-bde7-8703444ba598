(function() {
  // === 全局API拦截器配置区 ===

  // 拦截器配置
  const interceptorConfig = {
    enabled: true,           // 是否启用拦截器
    logLevel: 'info',       // 日志级别: 'none', 'error', 'warn', 'info', 'debug'
    logToConsole: true,     // 是否输出到控制台
    logToStorage: false,    // 是否保存到本地存储
    maxLogEntries: 100      // 最大日志条数
  };

  // 拦截规则配置
  const rules = [
    {
      name: "学生列表主拦截器",
      match: "tesystem/ngres/xueshengguanli/student/list",  // 匹配URL路径部分
      enabled: true,
      method: "ALL",  // 拦截所有HTTP方法
      modify: (data, url, method) => {
        // 添加拦截标记
        data.intercepted = true;
        data.interceptTime = new Date().toISOString();
        data.interceptedUrl = url;
        data.interceptedMethod = method;
        data.note = "✅ 此数据已被API拦截器成功修改";

        // 如果有学生列表数据，进行增强
        if (data.list && Array.isArray(data.list)) {
          data.originalCount = data.list.length;
          data.list.forEach((student, index) => {
            student.intercepted = true;
            student.序号 = index + 1;
            student.修改时间 = new Date().toISOString();
            student.备注 = "✅ 已被拦截器处理";
            // 可以在这里添加更多字段或修改现有字段
          });
          data.listEnhanced = true;
        }

        // 添加分页信息增强
        if (data.page) {
          data.page.intercepted = true;
          data.page.enhancedAt = new Date().toISOString();
        }

        // 添加调试信息
        data._debugInfo = {
          拦截器版本: "2.0.0",
          拦截时间: new Date().toLocaleString('zh-CN'),
          原始URL: url,
          请求方法: method,
          处理状态: "成功"
        };

        console.log("🎯 学生列表API已被拦截并修改:", {
          url: url,
          method: method,
          studentCount: data.list ? data.list.length : 0,
          timestamp: new Date().toISOString()
        });

        return data;
      }
    },
    {
      name: "学生列表POST专用拦截器",
      match: "tesystem/ngres/xueshengguanli/student/list",
      enabled: false, // 默认禁用，可以根据需要启用
      method: "POST",  // 只拦截POST请求
      modify: (data, url, method) => {
        // POST请求专用处理逻辑
        data.postIntercepted = true;
        data.postProcessedAt = new Date().toISOString();
        data.requestMethod = method;

        if (data.list && Array.isArray(data.list)) {
          // 为每个学生添加POST专用信息
          data.list.forEach((student, index) => {
            student.postProcessed = true;
            student.postIndex = index + 1;
            student.postModifiedBy = "POST专用拦截器";
            student.postTimestamp = new Date().toISOString();

            // 可以在这里添加POST请求特有的处理逻辑
            // 比如添加表单提交相关的信息
            if (student.name) {
              student.displayName = `【POST】${student.name}`;
            }
          });
        }

        // 添加POST请求特有的元数据
        data.postMetadata = {
          处理方式: "POST专用拦截",
          处理时间: new Date().toLocaleString('zh-CN'),
          请求类型: "表单提交",
          状态: "已处理"
        };

        console.log("📮 学生列表POST请求已被拦截:", {
          url: url,
          studentCount: data.list ? data.list.length : 0
        });

        return data;
      }
    }
  ];

  // 日志存储
  const logs = [];

  // 日志记录函数
  function log(level, message, data = null) {
    if (!interceptorConfig.logToConsole && !interceptorConfig.logToStorage) return;

    const logLevels = { none: 0, error: 1, warn: 2, info: 3, debug: 4 };
    const currentLevel = logLevels[interceptorConfig.logLevel] || 3;
    const messageLevel = logLevels[level] || 3;

    if (messageLevel > currentLevel) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level,
      message: message,
      data: data
    };

    if (interceptorConfig.logToConsole) {
      const consoleMethod = console[level] || console.log;
      if (data) {
        consoleMethod(`[API拦截器-${level.toUpperCase()}] ${message}`, data);
      } else {
        consoleMethod(`[API拦截器-${level.toUpperCase()}] ${message}`);
      }
    }

    if (interceptorConfig.logToStorage) {
      logs.push(logEntry);
      if (logs.length > interceptorConfig.maxLogEntries) {
        logs.shift(); // 移除最旧的日志
      }
      try {
        localStorage.setItem('apiInterceptorLogs', JSON.stringify(logs));
      } catch (e) {
        console.warn('无法保存拦截器日志到本地存储:', e);
      }
    }
  }

  // 应用拦截规则
  function applyRules(url, text, method = 'GET') {
    if (!interceptorConfig.enabled) return text;

    // 查找匹配的规则
    const matchedRules = rules.filter(rule => {
      if (!rule.enabled) return false;
      if (rule.method !== 'ALL' && rule.method !== method.toUpperCase()) return false;

      // 支持字符串匹配和正则表达式匹配
      if (typeof rule.match === 'string') {
        return url.includes(rule.match);
      } else if (rule.match instanceof RegExp) {
        return rule.match.test(url);
      }
      return false;
    });

    if (matchedRules.length === 0) return text;

    try {
      let modified = JSON.parse(text);
      const original = JSON.parse(text); // 保留原始数据用于日志

      // 依次应用所有匹配的规则
      matchedRules.forEach(rule => {
        try {
          const result = rule.modify(modified, url, method);
          if (result !== undefined) {
            modified = result;
          }
          log('info', `应用拦截规则: ${rule.name}`, { url, method, rule: rule.name });
        } catch (e) {
          log('error', `拦截规则执行失败: ${rule.name}`, { error: e.message, url, method });
        }
      });

      log('info', `API拦截成功: ${url}`, {
        url,
        method,
        appliedRules: matchedRules.map(r => r.name),
        original: original,
        modified: modified
      });

      return JSON.stringify(modified);
    } catch (e) {
      log('error', `拦截处理失败: ${url}`, { error: e.message, url, method });
      return text;
    }
  }

  // === 全局API管理接口 ===
  window.APIInterceptor = {
    // 配置管理
    config: interceptorConfig,

    // 规则管理
    rules: rules,

    // 添加新规则
    addRule: function(rule) {
      if (!rule.name || !rule.match || typeof rule.modify !== 'function') {
        log('error', '添加规则失败：规则格式不正确', rule);
        return false;
      }

      rule.enabled = rule.enabled !== false; // 默认启用
      rule.method = rule.method || 'ALL';

      rules.push(rule);
      log('info', `添加拦截规则: ${rule.name}`, rule);
      return true;
    },

    // 移除规则
    removeRule: function(ruleName) {
      const index = rules.findIndex(r => r.name === ruleName);
      if (index !== -1) {
        const removed = rules.splice(index, 1)[0];
        log('info', `移除拦截规则: ${ruleName}`, removed);
        return true;
      }
      log('warn', `未找到拦截规则: ${ruleName}`);
      return false;
    },

    // 启用/禁用规则
    toggleRule: function(ruleName, enabled) {
      const rule = rules.find(r => r.name === ruleName);
      if (rule) {
        rule.enabled = enabled;
        log('info', `${enabled ? '启用' : '禁用'}拦截规则: ${ruleName}`);
        return true;
      }
      log('warn', `未找到拦截规则: ${ruleName}`);
      return false;
    },

    // 启用/禁用拦截器
    toggle: function(enabled) {
      interceptorConfig.enabled = enabled;
      log('info', `拦截器已${enabled ? '启用' : '禁用'}`);
    },

    // 获取日志
    getLogs: function() {
      return logs.slice(); // 返回副本
    },

    // 清空日志
    clearLogs: function() {
      logs.length = 0;
      try {
        localStorage.removeItem('apiInterceptorLogs');
      } catch (e) {
        // 忽略错误
      }
      log('info', '日志已清空');
    },

    // 导出配置
    exportConfig: function() {
      return {
        config: { ...interceptorConfig },
        rules: rules.map(r => ({ ...r }))
      };
    },

    // 导入配置
    importConfig: function(config) {
      try {
        if (config.config) {
          Object.assign(interceptorConfig, config.config);
        }
        if (config.rules && Array.isArray(config.rules)) {
          rules.length = 0;
          rules.push(...config.rules);
        }
        log('info', '配置导入成功', config);
        return true;
      } catch (e) {
        log('error', '配置导入失败', { error: e.message, config });
        return false;
      }
    }
  };

  // === 拦截 XMLHttpRequest ===
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function(method, url, ...rest) {
    this._interceptorMethod = method;
    this._interceptorUrl = url;
    this._interceptorStartTime = Date.now();

    log('debug', `XHR请求开始: ${method} ${url}`);
    return originalXHROpen.call(this, method, url, ...rest);
  };

  XMLHttpRequest.prototype.send = function(body) {
    const self = this;

    this.addEventListener("readystatechange", function() {
      if (this.readyState === 4) {
        const duration = Date.now() - (self._interceptorStartTime || 0);

        if (this.status >= 200 && this.status < 300) {
          try {
            const modifiedText = applyRules(
              self._interceptorUrl,
              this.responseText,
              self._interceptorMethod
            );

            if (modifiedText !== this.responseText) {
              // 重写响应数据
              Object.defineProperty(this, "responseText", {
                value: modifiedText,
                writable: false
              });
              Object.defineProperty(this, "response", {
                value: modifiedText,
                writable: false
              });

              log('debug', `XHR响应已修改: ${self._interceptorMethod} ${self._interceptorUrl}`, {
                duration: `${duration}ms`,
                originalLength: this.responseText?.length || 0,
                modifiedLength: modifiedText?.length || 0
              });
            } else {
              log('debug', `XHR响应未修改: ${self._interceptorMethod} ${self._interceptorUrl}`, {
                duration: `${duration}ms`
              });
            }
          } catch (e) {
            log('error', `XHR响应处理失败: ${self._interceptorUrl}`, {
              error: e.message,
              duration: `${duration}ms`
            });
          }
        } else {
          log('debug', `XHR请求失败: ${self._interceptorMethod} ${self._interceptorUrl}`, {
            status: this.status,
            statusText: this.statusText,
            duration: `${duration}ms`
          });
        }
      }
    });

    return originalXHRSend.call(this, body);
  };

  // === 拦截 fetch API ===
  const originalFetch = window.fetch;

  window.fetch = async function(...args) {
    const startTime = Date.now();
    let url = '';
    let method = 'GET';

    // 解析请求参数
    if (typeof args[0] === 'string') {
      url = args[0];
      if (args[1] && args[1].method) {
        method = args[1].method.toUpperCase();
      }
    } else if (args[0] instanceof Request) {
      url = args[0].url;
      method = args[0].method.toUpperCase();
    }

    log('debug', `Fetch请求开始: ${method} ${url}`);

    try {
      const response = await originalFetch.apply(this, args);
      const duration = Date.now() - startTime;

      if (response.ok) {
        try {
          const clone = response.clone();
          const text = await clone.text();
          const modifiedText = applyRules(url, text, method);

          if (modifiedText !== text) {
            log('debug', `Fetch响应已修改: ${method} ${url}`, {
              duration: `${duration}ms`,
              originalLength: text?.length || 0,
              modifiedLength: modifiedText?.length || 0
            });

            return new Response(modifiedText, {
              status: response.status,
              statusText: response.statusText,
              headers: response.headers,
              url: response.url
            });
          } else {
            log('debug', `Fetch响应未修改: ${method} ${url}`, {
              duration: `${duration}ms`
            });
          }
        } catch (e) {
          log('error', `Fetch响应处理失败: ${url}`, {
            error: e.message,
            duration: `${duration}ms`
          });
        }
      } else {
        log('debug', `Fetch请求失败: ${method} ${url}`, {
          status: response.status,
          statusText: response.statusText,
          duration: `${duration}ms`
        });
      }

      return response;
    } catch (e) {
      const duration = Date.now() - startTime;
      log('error', `Fetch请求异常: ${method} ${url}`, {
        error: e.message,
        duration: `${duration}ms`
      });
      throw e;
    }
  };

  // === 初始化和启动信息 ===

  // 从本地存储恢复日志（如果启用）
  if (interceptorConfig.logToStorage) {
    try {
      const savedLogs = localStorage.getItem('apiInterceptorLogs');
      if (savedLogs) {
        const parsedLogs = JSON.parse(savedLogs);
        if (Array.isArray(parsedLogs)) {
          logs.push(...parsedLogs.slice(-interceptorConfig.maxLogEntries));
        }
      }
    } catch (e) {
      console.warn('无法从本地存储恢复拦截器日志:', e);
    }
  }

  // 启动日志
  log('info', '🚀 全局API拦截器已启动', {
    version: '2.0.0',
    enabledRules: rules.filter(r => r.enabled).length,
    totalRules: rules.length,
    config: interceptorConfig
  });

  // 在控制台显示使用说明
  if (interceptorConfig.logToConsole && interceptorConfig.logLevel !== 'none') {
    console.group('📖 API拦截器使用说明');
    console.log('🔧 配置拦截器: APIInterceptor.config');
    console.log('📋 查看规则: APIInterceptor.rules');
    console.log('➕ 添加规则: APIInterceptor.addRule(rule)');
    console.log('❌ 移除规则: APIInterceptor.removeRule(ruleName)');
    console.log('🔄 切换规则: APIInterceptor.toggleRule(ruleName, enabled)');
    console.log('⚡ 启用/禁用: APIInterceptor.toggle(enabled)');
    console.log('📊 查看日志: APIInterceptor.getLogs()');
    console.log('🧹 清空日志: APIInterceptor.clearLogs()');
    console.log('💾 导出配置: APIInterceptor.exportConfig()');
    console.log('📥 导入配置: APIInterceptor.importConfig(config)');
    console.groupEnd();
  }

})();