<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API拦截器演示和管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #007bff;
            border-left: 4px solid #007bff;
            padding-left: 10px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #218838;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .rule-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        .rule-item.disabled {
            opacity: 0.6;
            background: #e9ecef;
        }
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .rule-name {
            font-weight: bold;
            color: #333;
        }
        .rule-controls {
            display: flex;
            gap: 5px;
        }
        .log-entry {
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry.error {
            border-left-color: #dc3545;
        }
        .log-entry.warn {
            border-left-color: #ffc107;
        }
        .log-entry.info {
            border-left-color: #17a2b8;
        }
        .log-entry.debug {
            border-left-color: #6c757d;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ API拦截器管理控制台</h1>
            <p>全局API请求拦截器的配置和管理界面</p>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="showTab('overview')">概览</div>
            <div class="tab" onclick="showTab('rules')">拦截规则</div>
            <div class="tab" onclick="showTab('logs')">日志查看</div>
            <div class="tab" onclick="showTab('config')">配置管理</div>
            <div class="tab" onclick="showTab('demo')">测试演示</div>
        </div>

        <!-- 概览标签页 -->
        <div id="overview" class="tab-content active">
            <div class="section">
                <h3>📊 拦截器状态</h3>
                <div id="interceptor-status"></div>
                <button class="button" onclick="toggleInterceptor()">启用/禁用拦截器</button>
                <button class="button" onclick="refreshStatus()">刷新状态</button>
            </div>

            <div class="section">
                <h3>📋 快速操作</h3>
                <button class="button success" onclick="addSampleRule()">添加示例规则</button>
                <button class="button" onclick="exportConfig()">导出配置</button>
                <button class="button" onclick="document.getElementById('import-file').click()">导入配置</button>
                <input type="file" id="import-file" style="display: none" accept=".json" onchange="importConfig(this)">
                <button class="button danger" onclick="clearAllLogs()">清空日志</button>
            </div>

            <div class="section">
                <h3>📖 使用说明</h3>
                <div class="code-block">
// 在浏览器控制台中使用以下命令：

// 查看当前配置
APIInterceptor.config

// 查看所有规则
APIInterceptor.rules

// 添加新的拦截规则
APIInterceptor.addRule({
    name: "我的拦截规则",
    match: "/api/users",  // 或使用正则: /\/api\/users\/\d+/
    enabled: true,
    method: "GET",  // GET, POST, PUT, DELETE, ALL
    modify: (data, url, method) => {
        // 修改返回数据
        data.intercepted = true;
        return data;
    }
});

// 启用/禁用规则
APIInterceptor.toggleRule("规则名称", true/false);

// 移除规则
APIInterceptor.removeRule("规则名称");

// 查看拦截日志
APIInterceptor.getLogs();
                </div>
            </div>
        </div>

        <!-- 拦截规则标签页 -->
        <div id="rules" class="tab-content">
            <div class="section">
                <h3>➕ 添加新规则</h3>
                <div class="form-group">
                    <label>规则名称:</label>
                    <input type="text" id="rule-name" placeholder="输入规则名称">
                </div>
                <div class="form-group">
                    <label>匹配模式:</label>
                    <input type="text" id="rule-match" placeholder="URL包含的字符串或正则表达式">
                </div>
                <div class="form-group">
                    <label>HTTP方法:</label>
                    <select id="rule-method">
                        <option value="ALL">ALL</option>
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>修改函数 (JavaScript):</label>
                    <textarea id="rule-modify" placeholder="(data, url, method) => { /* 修改data并返回 */ return data; }"></textarea>
                </div>
                <button class="button success" onclick="addCustomRule()">添加规则</button>
            </div>

            <div class="section">
                <h3>📋 现有规则</h3>
                <div id="rules-list"></div>
            </div>
        </div>

        <!-- 日志查看标签页 -->
        <div id="logs" class="tab-content">
            <div class="section">
                <h3>📊 拦截日志</h3>
                <button class="button" onclick="refreshLogs()">刷新日志</button>
                <button class="button danger" onclick="clearAllLogs()">清空日志</button>
                <div id="logs-container"></div>
            </div>
        </div>

        <!-- 配置管理标签页 -->
        <div id="config" class="tab-content">
            <div class="section">
                <h3>⚙️ 拦截器配置</h3>
                <div id="config-form"></div>
                <button class="button success" onclick="saveConfig()">保存配置</button>
            </div>
        </div>

        <!-- 测试演示标签页 -->
        <div id="demo" class="tab-content">
            <div class="section">
                <h3>🧪 测试API拦截</h3>
                <p>点击下面的按钮来测试API拦截功能：</p>
                <button class="button" onclick="testXHR()">测试 XMLHttpRequest</button>
                <button class="button" onclick="testFetch()">测试 Fetch API</button>
                <div id="test-results"></div>
            </div>
        </div>
    </div>

    <script>
        // 确保拦截器已加载
        if (typeof window.APIInterceptor === 'undefined') {
            document.body.innerHTML = '<div class="container"><div class="status error">❌ API拦截器未加载，请确保已引入 filter.js 文件</div></div>';
        }
    </script>
    <script src="js/filter.js"></script>
    <script>
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 刷新对应内容
            if (tabName === 'overview') refreshStatus();
            if (tabName === 'rules') refreshRules();
            if (tabName === 'logs') refreshLogs();
            if (tabName === 'config') refreshConfig();
        }

        // 刷新拦截器状态
        function refreshStatus() {
            const status = document.getElementById('interceptor-status');
            if (!window.APIInterceptor) {
                status.innerHTML = '<div class="status error">❌ API拦截器未加载</div>';
                return;
            }
            
            const config = window.APIInterceptor.config;
            const rules = window.APIInterceptor.rules;
            const enabledRules = rules.filter(r => r.enabled);
            
            status.innerHTML = `
                <div class="status ${config.enabled ? 'success' : 'error'}">
                    拦截器状态: ${config.enabled ? '✅ 已启用' : '❌ 已禁用'}
                </div>
                <div class="status info">
                    📊 统计信息:<br>
                    • 总规则数: ${rules.length}<br>
                    • 启用规则数: ${enabledRules.length}<br>
                    • 日志级别: ${config.logLevel}<br>
                    • 日志条数: ${window.APIInterceptor.getLogs().length}
                </div>
            `;
        }

        // 切换拦截器状态
        function toggleInterceptor() {
            if (!window.APIInterceptor) return;
            
            const currentState = window.APIInterceptor.config.enabled;
            window.APIInterceptor.toggle(!currentState);
            refreshStatus();
        }

        // 添加示例规则
        function addSampleRule() {
            if (!window.APIInterceptor) return;
            
            const sampleRule = {
                name: `示例规则_${Date.now()}`,
                match: "example.com/api",
                enabled: true,
                method: "ALL",
                modify: (data, url, method) => {
                    data.sampleModification = true;
                    data.modifiedAt = new Date().toISOString();
                    return data;
                }
            };
            
            if (window.APIInterceptor.addRule(sampleRule)) {
                showMessage('success', '示例规则添加成功！');
                refreshRules();
            } else {
                showMessage('error', '示例规则添加失败！');
            }
        }

        // 显示消息
        function showMessage(type, message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `status ${type}`;
            messageDiv.textContent = message;
            
            document.body.insertBefore(messageDiv, document.body.firstChild);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 导出配置
        function exportConfig() {
            if (!window.APIInterceptor) return;
            
            const config = window.APIInterceptor.exportConfig();
            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `api-interceptor-config-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            showMessage('success', '配置已导出！');
        }

        // 导入配置
        function importConfig(input) {
            if (!window.APIInterceptor || !input.files[0]) return;
            
            const file = input.files[0];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const config = JSON.parse(e.target.result);
                    if (window.APIInterceptor.importConfig(config)) {
                        showMessage('success', '配置导入成功！');
                        refreshStatus();
                        refreshRules();
                        refreshConfig();
                    } else {
                        showMessage('error', '配置导入失败！');
                    }
                } catch (error) {
                    showMessage('error', '配置文件格式错误！');
                }
            };
            
            reader.readAsText(file);
            input.value = ''; // 清空文件选择
        }

        // 清空所有日志
        function clearAllLogs() {
            if (!window.APIInterceptor) return;
            
            if (confirm('确定要清空所有日志吗？')) {
                window.APIInterceptor.clearLogs();
                showMessage('success', '日志已清空！');
                refreshLogs();
            }
        }

        // 刷新规则列表
        function refreshRules() {
            if (!window.APIInterceptor) return;

            const rulesList = document.getElementById('rules-list');
            const rules = window.APIInterceptor.rules;

            if (rules.length === 0) {
                rulesList.innerHTML = '<div class="status info">暂无拦截规则</div>';
                return;
            }

            rulesList.innerHTML = rules.map((rule, index) => `
                <div class="rule-item ${rule.enabled ? '' : 'disabled'}">
                    <div class="rule-header">
                        <div class="rule-name">${rule.name}</div>
                        <div class="rule-controls">
                            <button class="button ${rule.enabled ? 'danger' : 'success'}"
                                    onclick="toggleRule('${rule.name}', ${!rule.enabled})">
                                ${rule.enabled ? '禁用' : '启用'}
                            </button>
                            <button class="button danger" onclick="removeRule('${rule.name}')">删除</button>
                        </div>
                    </div>
                    <div><strong>匹配:</strong> ${rule.match}</div>
                    <div><strong>方法:</strong> ${rule.method}</div>
                    <div><strong>状态:</strong> ${rule.enabled ? '✅ 启用' : '❌ 禁用'}</div>
                </div>
            `).join('');
        }

        // 切换规则状态
        function toggleRule(ruleName, enabled) {
            if (!window.APIInterceptor) return;

            if (window.APIInterceptor.toggleRule(ruleName, enabled)) {
                showMessage('success', `规则 "${ruleName}" 已${enabled ? '启用' : '禁用'}！`);
                refreshRules();
                refreshStatus();
            } else {
                showMessage('error', '规则状态切换失败！');
            }
        }

        // 移除规则
        function removeRule(ruleName) {
            if (!window.APIInterceptor) return;

            if (confirm(`确定要删除规则 "${ruleName}" 吗？`)) {
                if (window.APIInterceptor.removeRule(ruleName)) {
                    showMessage('success', `规则 "${ruleName}" 已删除！`);
                    refreshRules();
                    refreshStatus();
                } else {
                    showMessage('error', '规则删除失败！');
                }
            }
        }

        // 添加自定义规则
        function addCustomRule() {
            if (!window.APIInterceptor) return;

            const name = document.getElementById('rule-name').value.trim();
            const match = document.getElementById('rule-match').value.trim();
            const method = document.getElementById('rule-method').value;
            const modifyCode = document.getElementById('rule-modify').value.trim();

            if (!name || !match || !modifyCode) {
                showMessage('error', '请填写所有必填字段！');
                return;
            }

            try {
                // 验证修改函数
                const modifyFunction = new Function('data', 'url', 'method', modifyCode);

                const rule = {
                    name: name,
                    match: match,
                    enabled: true,
                    method: method,
                    modify: modifyFunction
                };

                if (window.APIInterceptor.addRule(rule)) {
                    showMessage('success', `规则 "${name}" 添加成功！`);
                    // 清空表单
                    document.getElementById('rule-name').value = '';
                    document.getElementById('rule-match').value = '';
                    document.getElementById('rule-modify').value = '';
                    refreshRules();
                    refreshStatus();
                } else {
                    showMessage('error', '规则添加失败！');
                }
            } catch (error) {
                showMessage('error', `修改函数语法错误: ${error.message}`);
            }
        }

        // 刷新日志
        function refreshLogs() {
            if (!window.APIInterceptor) return;

            const logsContainer = document.getElementById('logs-container');
            const logs = window.APIInterceptor.getLogs();

            if (logs.length === 0) {
                logsContainer.innerHTML = '<div class="status info">暂无日志记录</div>';
                return;
            }

            logsContainer.innerHTML = logs.slice(-50).reverse().map(log => `
                <div class="log-entry ${log.level}">
                    <div><strong>[${log.timestamp}] ${log.level.toUpperCase()}</strong></div>
                    <div>${log.message}</div>
                    ${log.data ? `<div><pre>${JSON.stringify(log.data, null, 2)}</pre></div>` : ''}
                </div>
            `).join('');
        }

        // 刷新配置
        function refreshConfig() {
            if (!window.APIInterceptor) return;

            const configForm = document.getElementById('config-form');
            const config = window.APIInterceptor.config;

            configForm.innerHTML = `
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="config-enabled" ${config.enabled ? 'checked' : ''}>
                        启用拦截器
                    </label>
                </div>
                <div class="form-group">
                    <label>日志级别:</label>
                    <select id="config-logLevel">
                        <option value="none" ${config.logLevel === 'none' ? 'selected' : ''}>无</option>
                        <option value="error" ${config.logLevel === 'error' ? 'selected' : ''}>错误</option>
                        <option value="warn" ${config.logLevel === 'warn' ? 'selected' : ''}>警告</option>
                        <option value="info" ${config.logLevel === 'info' ? 'selected' : ''}>信息</option>
                        <option value="debug" ${config.logLevel === 'debug' ? 'selected' : ''}>调试</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="config-logToConsole" ${config.logToConsole ? 'checked' : ''}>
                        输出到控制台
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="config-logToStorage" ${config.logToStorage ? 'checked' : ''}>
                        保存到本地存储
                    </label>
                </div>
                <div class="form-group">
                    <label>最大日志条数:</label>
                    <input type="number" id="config-maxLogEntries" value="${config.maxLogEntries}" min="10" max="1000">
                </div>
            `;
        }

        // 保存配置
        function saveConfig() {
            if (!window.APIInterceptor) return;

            const config = window.APIInterceptor.config;

            config.enabled = document.getElementById('config-enabled').checked;
            config.logLevel = document.getElementById('config-logLevel').value;
            config.logToConsole = document.getElementById('config-logToConsole').checked;
            config.logToStorage = document.getElementById('config-logToStorage').checked;
            config.maxLogEntries = parseInt(document.getElementById('config-maxLogEntries').value);

            showMessage('success', '配置已保存！');
            refreshStatus();
        }

        // 测试 XMLHttpRequest
        function testXHR() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">正在测试 XMLHttpRequest...</div>';

            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://jsonplaceholder.typicode.com/posts/1');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    resultsDiv.innerHTML = `
                        <div class="status success">✅ XMLHttpRequest 测试成功</div>
                        <div class="code-block">${xhr.responseText}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="status error">❌ XMLHttpRequest 测试失败: ${xhr.status}</div>`;
                }
            };
            xhr.onerror = function() {
                resultsDiv.innerHTML = '<div class="status error">❌ XMLHttpRequest 测试出错</div>';
            };
            xhr.send();
        }

        // 测试 Fetch API
        function testFetch() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">正在测试 Fetch API...</div>';

            fetch('https://jsonplaceholder.typicode.com/posts/2')
                .then(response => response.text())
                .then(data => {
                    resultsDiv.innerHTML = `
                        <div class="status success">✅ Fetch API 测试成功</div>
                        <div class="code-block">${data}</div>
                    `;
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<div class="status error">❌ Fetch API 测试失败: ${error.message}</div>`;
                });
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
        });
    </script>
</body>
</html>
