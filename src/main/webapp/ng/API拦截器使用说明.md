# 全局API拦截器使用说明

## 概述

这是一个强大的全局API拦截器，可以拦截和修改所有的HTTP请求返回值。支持XMLHttpRequest和Fetch API，提供灵活的配置选项和完整的日志记录功能。

## 功能特性

- ✅ **全面拦截**: 支持XMLHttpRequest和Fetch API
- ✅ **灵活配置**: 支持字符串匹配和正则表达式匹配
- ✅ **方法过滤**: 可按HTTP方法（GET、POST等）进行过滤
- ✅ **实时修改**: 可以实时修改API返回数据
- ✅ **日志记录**: 完整的拦截日志，支持多种日志级别
- ✅ **配置管理**: 支持配置导入导出
- ✅ **可视化管理**: 提供Web界面进行管理

## 快速开始

### 1. 引入拦截器

拦截器已经在 `index.html` 中引入：

```html
<script src="js/filter.js" type="text/javascript"></script>
```

### 2. 基本使用

在浏览器控制台中使用：

```javascript
// 查看拦截器状态
APIInterceptor.config

// 添加拦截规则
APIInterceptor.addRule({
    name: "用户信息拦截",
    match: "/api/users",
    enabled: true,
    method: "GET",
    modify: (data, url, method) => {
        // 修改返回数据
        data.intercepted = true;
        data.timestamp = new Date().toISOString();
        return data;
    }
});
```

### 3. 管理界面

访问 `api-interceptor-demo.html` 来使用可视化管理界面。

## API参考

### 全局对象：`window.APIInterceptor`

#### 配置管理

```javascript
// 查看当前配置
APIInterceptor.config

// 启用/禁用拦截器
APIInterceptor.toggle(true/false)
```

#### 规则管理

```javascript
// 查看所有规则
APIInterceptor.rules

// 添加规则
APIInterceptor.addRule({
    name: "规则名称",           // 必填：规则名称
    match: "匹配模式",          // 必填：URL匹配模式
    enabled: true,             // 可选：是否启用，默认true
    method: "ALL",             // 可选：HTTP方法，默认ALL
    modify: (data, url, method) => {  // 必填：修改函数
        // 处理数据
        return data;
    }
})

// 启用/禁用规则
APIInterceptor.toggleRule("规则名称", true/false)

// 移除规则
APIInterceptor.removeRule("规则名称")
```

#### 日志管理

```javascript
// 查看日志
APIInterceptor.getLogs()

// 清空日志
APIInterceptor.clearLogs()
```

#### 配置导入导出

```javascript
// 导出配置
const config = APIInterceptor.exportConfig()

// 导入配置
APIInterceptor.importConfig(config)
```

## 规则配置详解

### 匹配模式 (match)

支持两种匹配方式：

1. **字符串匹配**：
   ```javascript
   match: "/api/users"  // 匹配包含此字符串的URL
   ```

2. **正则表达式匹配**：
   ```javascript
   match: /\/api\/users\/\d+/  // 匹配符合正则的URL
   ```

### HTTP方法过滤 (method)

- `"ALL"`: 匹配所有HTTP方法（默认）
- `"GET"`: 只匹配GET请求
- `"POST"`: 只匹配POST请求
- `"PUT"`: 只匹配PUT请求
- `"DELETE"`: 只匹配DELETE请求

### 修改函数 (modify)

修改函数接收三个参数：

```javascript
modify: (data, url, method) => {
    // data: 解析后的JSON数据对象
    // url: 请求的URL
    // method: HTTP方法
    
    // 修改数据
    data.modified = true;
    
    // 必须返回修改后的数据
    return data;
}
```

## 配置选项

```javascript
APIInterceptor.config = {
    enabled: true,           // 是否启用拦截器
    logLevel: 'info',       // 日志级别: none, error, warn, info, debug
    logToConsole: true,     // 是否输出到控制台
    logToStorage: false,    // 是否保存到本地存储
    maxLogEntries: 100      // 最大日志条数
}
```

## 使用示例

### 示例1：修改学生列表数据

```javascript
APIInterceptor.addRule({
    name: "学生列表增强",
    match: "xueshengguanli/student/list",
    method: "POST",
    modify: (data, url, method) => {
        if (data.list && Array.isArray(data.list)) {
            data.list.forEach(student => {
                student.enhanced = true;
                student.lastModified = new Date().toISOString();
            });
        }
        return data;
    }
});
```

### 示例2：添加调试信息

```javascript
APIInterceptor.addRule({
    name: "调试信息注入",
    match: /\/api\/.*/,
    method: "ALL",
    modify: (data, url, method) => {
        data._debug = {
            interceptedAt: new Date().toISOString(),
            url: url,
            method: method,
            originalKeys: Object.keys(data)
        };
        return data;
    }
});
```

### 示例3：模拟数据

```javascript
APIInterceptor.addRule({
    name: "用户信息模拟",
    match: "/api/userinfo",
    method: "GET",
    modify: (data, url, method) => {
        return {
            id: 999,
            name: "测试用户",
            email: "<EMAIL>",
            role: "admin",
            simulated: true
        };
    }
});
```

## 注意事项

1. **性能影响**: 拦截器会对所有匹配的请求进行处理，可能会有轻微的性能影响
2. **错误处理**: 如果修改函数出错，会返回原始数据并记录错误日志
3. **数据格式**: 只能处理JSON格式的响应数据
4. **浏览器兼容性**: 支持现代浏览器，需要ES6+支持

## 故障排除

### 拦截器未生效

1. 检查拦截器是否启用：`APIInterceptor.config.enabled`
2. 检查规则是否启用：`APIInterceptor.rules`
3. 检查匹配模式是否正确
4. 查看控制台错误信息

### 修改函数不工作

1. 检查函数语法是否正确
2. 确保返回了修改后的数据
3. 检查数据格式是否为JSON
4. 查看拦截日志：`APIInterceptor.getLogs()`

### 日志不显示

1. 检查日志级别设置：`APIInterceptor.config.logLevel`
2. 检查控制台输出设置：`APIInterceptor.config.logToConsole`
3. 尝试刷新页面重新加载拦截器

## 更新日志

### v2.0.0
- 重构代码架构，提供更好的API
- 添加可视化管理界面
- 支持配置导入导出
- 增强日志记录功能
- 添加性能监控

### v1.0.0
- 基础拦截功能
- 支持XHR和Fetch
- 简单的规则配置
