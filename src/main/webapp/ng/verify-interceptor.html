<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证API拦截器数据替换</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #007bff;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 验证API拦截器数据替换</h1>
            <p>验证拦截器是否成功替换了原始API返回数据</p>
        </div>

        <div class="container">
            <h3>📋 测试步骤</h3>
            <button class="button success" onclick="testDataReplacement()">🧪 测试数据替换</button>
            <button class="button" onclick="testWithoutInterceptor()">🔄 测试原始数据（禁用拦截器）</button>
            <button class="button" onclick="compareResults()">📊 对比结果</button>
            <button class="button danger" onclick="clearResults()">🧹 清空结果</button>
        </div>

        <div id="test-results"></div>

        <div class="container">
            <h3>🎯 验证要点</h3>
            <ul>
                <li><strong>数据完整性</strong>: 修改后的数据应该包含所有原始字段</li>
                <li><strong>新增字段</strong>: 应该看到拦截器添加的字段（如 <code>intercepted: true</code>）</li>
                <li><strong>数据一致性</strong>: 前端应用应该能正常使用修改后的数据</li>
                <li><strong>性能影响</strong>: 拦截过程不应该显著影响响应时间</li>
            </ul>
        </div>

        <div class="container">
            <h3>📖 如何确认数据被替换</h3>
            <div class="status info">
                <strong>方法1: 检查响应对象</strong><br>
                在网络请求完成后，检查 <code>xhr.responseText</code> 或 <code>response.json()</code> 的内容
            </div>
            <div class="status info">
                <strong>方法2: 查看网络面板</strong><br>
                打开浏览器开发者工具的Network面板，查看实际的响应内容
            </div>
            <div class="status info">
                <strong>方法3: 前端数据验证</strong><br>
                在前端代码中检查是否能访问到拦截器添加的字段
            </div>
        </div>
    </div>

    <!-- 引入拦截器 -->
    <script src="js/filter.js"></script>

    <script>
        let originalData = null;
        let interceptedData = null;

        // 测试数据替换
        async function testDataReplacement() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">🧪 正在测试数据替换...</div>';

            try {
                // 确保拦截器启用
                if (window.APIInterceptor) {
                    window.APIInterceptor.toggle(true);
                }

                // 发送请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '../ngres/xueshengguanli/student/list');
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            interceptedData = data;
                            
                            // 检查是否被拦截
                            const isIntercepted = data.intercepted === true || data.universalIntercepted === true;
                            
                            let resultHtml = `
                                <div class="status ${isIntercepted ? 'success' : 'error'}">
                                    ${isIntercepted ? '✅ 数据替换成功！' : '❌ 数据未被拦截'}
                                </div>
                            `;

                            if (isIntercepted) {
                                resultHtml += `
                                    <div class="container">
                                        <h4>🎯 拦截验证结果</h4>
                                        <ul>
                                            <li>✅ 检测到拦截标记: <span class="highlight">intercepted: ${data.intercepted}</span></li>
                                            <li>✅ 拦截时间: <span class="highlight">${data.interceptTime || data.universalInterceptTime}</span></li>
                                            <li>✅ 拦截URL: <span class="highlight">${data.interceptedUrl}</span></li>
                                            <li>✅ 请求方法: <span class="highlight">${data.interceptedMethod}</span></li>
                                            ${data.originalCount ? `<li>✅ 学生数量: <span class="highlight">${data.originalCount}</span></li>` : ''}
                                            ${data._debugInfo ? `<li>✅ 调试信息: <span class="highlight">已添加</span></li>` : ''}
                                        </ul>
                                    </div>
                                `;

                                // 检查学生列表是否被修改
                                if (data.list && Array.isArray(data.list) && data.list.length > 0) {
                                    const firstStudent = data.list[0];
                                    const studentModified = firstStudent.intercepted === true;
                                    
                                    resultHtml += `
                                        <div class="container">
                                            <h4>👥 学生数据验证</h4>
                                            <p>第一个学生数据修改状态: <span class="highlight">${studentModified ? '✅ 已修改' : '❌ 未修改'}</span></p>
                                            ${studentModified ? `
                                                <ul>
                                                    <li>序号: <span class="highlight">${firstStudent.序号}</span></li>
                                                    <li>修改时间: <span class="highlight">${firstStudent.修改时间}</span></li>
                                                    <li>备注: <span class="highlight">${firstStudent.备注}</span></li>
                                                </ul>
                                            ` : ''}
                                        </div>
                                    `;
                                }
                            }

                            resultHtml += `
                                <div class="container">
                                    <h4>📄 完整响应数据</h4>
                                    <div class="result">${JSON.stringify(data, null, 2)}</div>
                                </div>
                            `;

                            resultsDiv.innerHTML = resultHtml;

                        } catch (e) {
                            resultsDiv.innerHTML = `
                                <div class="status error">❌ 数据解析失败: ${e.message}</div>
                                <div class="result">${xhr.responseText}</div>
                            `;
                        }
                    } else {
                        resultsDiv.innerHTML = `<div class="status error">❌ 请求失败: ${xhr.status}</div>`;
                    }
                };

                xhr.onerror = function() {
                    resultsDiv.innerHTML = '<div class="status error">❌ 网络错误</div>';
                };

                // 发送请求
                xhr.send(JSON.stringify({
                    page: { currentPage: 1, itemsperpage: 10 },
                    searchItems: {}
                }));

            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 测试原始数据（禁用拦截器）
        async function testWithoutInterceptor() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status info">🔄 正在获取原始数据（拦截器已禁用）...</div>';

            try {
                // 禁用拦截器
                if (window.APIInterceptor) {
                    window.APIInterceptor.toggle(false);
                }

                // 发送请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '../ngres/xueshengguanli/student/list');
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            originalData = data;
                            
                            const resultHtml = `
                                <div class="status info">📄 原始数据（未拦截）</div>
                                <div class="container">
                                    <h4>📊 原始数据统计</h4>
                                    <ul>
                                        <li>学生数量: <span class="highlight">${data.list ? data.list.length : 0}</span></li>
                                        <li>总页数: <span class="highlight">${data.page ? data.page.totalPages : 'N/A'}</span></li>
                                        <li>总记录数: <span class="highlight">${data.page ? data.page.totalItems : 'N/A'}</span></li>
                                        <li>拦截标记: <span class="highlight">${data.intercepted ? '存在' : '不存在'}</span></li>
                                    </ul>
                                </div>
                                <div class="container">
                                    <h4>📄 完整原始数据</h4>
                                    <div class="result">${JSON.stringify(data, null, 2)}</div>
                                </div>
                            `;

                            resultsDiv.innerHTML = resultHtml;

                            // 重新启用拦截器
                            if (window.APIInterceptor) {
                                window.APIInterceptor.toggle(true);
                            }

                        } catch (e) {
                            resultsDiv.innerHTML = `
                                <div class="status error">❌ 数据解析失败: ${e.message}</div>
                                <div class="result">${xhr.responseText}</div>
                            `;
                        }
                    } else {
                        resultsDiv.innerHTML = `<div class="status error">❌ 请求失败: ${xhr.status}</div>`;
                    }
                };

                xhr.onerror = function() {
                    resultsDiv.innerHTML = '<div class="status error">❌ 网络错误</div>';
                };

                // 发送请求
                xhr.send(JSON.stringify({
                    page: { currentPage: 1, itemsperpage: 10 },
                    searchItems: {}
                }));

            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ 测试失败: ${error.message}</div>`;
            }
        }

        // 对比结果
        function compareResults() {
            const resultsDiv = document.getElementById('test-results');
            
            if (!originalData || !interceptedData) {
                resultsDiv.innerHTML = `
                    <div class="status error">❌ 请先运行两个测试以获取对比数据</div>
                    <p>1. 点击"测试原始数据"获取未拦截的数据</p>
                    <p>2. 点击"测试数据替换"获取拦截后的数据</p>
                `;
                return;
            }

            const originalKeys = Object.keys(originalData);
            const interceptedKeys = Object.keys(interceptedData);
            const newKeys = interceptedKeys.filter(key => !originalKeys.includes(key));
            const modifiedKeys = originalKeys.filter(key => 
                JSON.stringify(originalData[key]) !== JSON.stringify(interceptedData[key])
            );

            const resultHtml = `
                <div class="status success">📊 数据对比结果</div>
                
                <div class="comparison">
                    <div class="comparison-item">
                        <h4>📄 原始数据</h4>
                        <ul>
                            <li>字段数量: ${originalKeys.length}</li>
                            <li>学生数量: ${originalData.list ? originalData.list.length : 0}</li>
                            <li>拦截标记: ${originalData.intercepted ? '有' : '无'}</li>
                        </ul>
                    </div>
                    
                    <div class="comparison-item">
                        <h4>🔄 拦截后数据</h4>
                        <ul>
                            <li>字段数量: ${interceptedKeys.length}</li>
                            <li>学生数量: ${interceptedData.list ? interceptedData.list.length : 0}</li>
                            <li>拦截标记: ${interceptedData.intercepted ? '有' : '无'}</li>
                        </ul>
                    </div>
                </div>

                <div class="container">
                    <h4>🆕 新增字段 (${newKeys.length}个)</h4>
                    ${newKeys.length > 0 ? `
                        <ul>
                            ${newKeys.map(key => `<li><span class="highlight">${key}</span>: ${JSON.stringify(interceptedData[key])}</li>`).join('')}
                        </ul>
                    ` : '<p>无新增字段</p>'}
                </div>

                <div class="container">
                    <h4>🔄 修改字段 (${modifiedKeys.length}个)</h4>
                    ${modifiedKeys.length > 0 ? `
                        <ul>
                            ${modifiedKeys.map(key => `<li><span class="highlight">${key}</span>: 已修改</li>`).join('')}
                        </ul>
                    ` : '<p>无修改字段</p>'}
                </div>

                <div class="container">
                    <h4>✅ 验证结论</h4>
                    <div class="status ${newKeys.length > 0 ? 'success' : 'error'}">
                        ${newKeys.length > 0 ? 
                            '✅ 数据替换成功！拦截器成功添加了新字段并修改了原始数据。' : 
                            '❌ 数据替换失败！未检测到拦截器的修改。'
                        }
                    </div>
                </div>
            `;

            resultsDiv.innerHTML = resultHtml;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            originalData = null;
            interceptedData = null;
        }

        // 页面加载时的说明
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = `
                <div class="status info">
                    <h4>🚀 开始验证</h4>
                    <p>点击上方按钮开始测试API拦截器的数据替换功能。</p>
                    <p><strong>建议测试顺序：</strong></p>
                    <ol>
                        <li>先点击"测试数据替换"查看拦截效果</li>
                        <li>再点击"测试原始数据"获取未拦截的数据</li>
                        <li>最后点击"对比结果"查看详细对比</li>
                    </ol>
                </div>
            `;
        });
    </script>
</body>
</html>
