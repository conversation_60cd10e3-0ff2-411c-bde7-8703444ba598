<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API拦截器测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #007bff;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 API拦截器测试页面</h1>
            <p>测试全局API拦截器的功能</p>
        </div>

        <div id="interceptor-status"></div>

        <div class="test-section">
            <h3>📋 拦截器控制</h3>
            <button class="button" onclick="checkInterceptor()">检查拦截器状态</button>
            <button class="button success" onclick="enableInterceptor()">启用拦截器</button>
            <button class="button danger" onclick="disableInterceptor()">禁用拦截器</button>
            <button class="button" onclick="showRules()">显示拦截规则</button>
        </div>

        <div class="test-section">
            <h3>🔧 添加测试规则</h3>
            <button class="button success" onclick="addTestRule()">添加JSONPlaceholder测试规则</button>
            <button class="button success" onclick="addLocalTestRule()">添加本地API测试规则</button>
            <button class="button danger" onclick="clearTestRules()">清除测试规则</button>
        </div>

        <div class="test-section">
            <h3>🌐 外部API测试</h3>
            <p>测试对外部API的拦截（需要网络连接）</p>
            <button class="button" onclick="testExternalXHR()">测试外部XHR请求</button>
            <button class="button" onclick="testExternalFetch()">测试外部Fetch请求</button>
            <div id="external-results"></div>
        </div>

        <div class="test-section">
            <h3>🏠 本地API测试</h3>
            <p>测试对本地API的拦截</p>
            <button class="button" onclick="testLocalAPI()">测试学生列表API</button>
            <button class="button" onclick="testMockAPI()">测试模拟API</button>
            <div id="local-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 拦截日志</h3>
            <button class="button" onclick="showLogs()">显示拦截日志</button>
            <button class="button danger" onclick="clearLogs()">清空日志</button>
            <div id="logs-display"></div>
        </div>
    </div>

    <!-- 引入拦截器 -->
    <script src="js/filter.js"></script>

    <script>
        // 检查拦截器状态
        function checkInterceptor() {
            const statusDiv = document.getElementById('interceptor-status');
            
            if (typeof window.APIInterceptor === 'undefined') {
                statusDiv.innerHTML = '<div class="status error">❌ API拦截器未加载</div>';
                return false;
            }
            
            const config = window.APIInterceptor.config;
            const rules = window.APIInterceptor.rules;
            const enabledRules = rules.filter(r => r.enabled);
            
            statusDiv.innerHTML = `
                <div class="status ${config.enabled ? 'success' : 'error'}">
                    拦截器状态: ${config.enabled ? '✅ 已启用' : '❌ 已禁用'}
                </div>
                <div class="status info">
                    📊 统计信息:<br>
                    • 总规则数: ${rules.length}<br>
                    • 启用规则数: ${enabledRules.length}<br>
                    • 日志级别: ${config.logLevel}<br>
                    • 日志条数: ${window.APIInterceptor.getLogs().length}
                </div>
            `;
            return true;
        }

        // 启用拦截器
        function enableInterceptor() {
            if (window.APIInterceptor) {
                window.APIInterceptor.toggle(true);
                checkInterceptor();
                showMessage('success', '拦截器已启用');
            }
        }

        // 禁用拦截器
        function disableInterceptor() {
            if (window.APIInterceptor) {
                window.APIInterceptor.toggle(false);
                checkInterceptor();
                showMessage('error', '拦截器已禁用');
            }
        }

        // 显示规则
        function showRules() {
            if (!window.APIInterceptor) return;
            
            const rules = window.APIInterceptor.rules;
            const rulesText = rules.map((rule, index) => 
                `${index + 1}. ${rule.name} (${rule.enabled ? '启用' : '禁用'}) - ${rule.match}`
            ).join('\n');
            
            showResult('当前拦截规则:\n\n' + (rulesText || '暂无规则'));
        }

        // 添加JSONPlaceholder测试规则
        function addTestRule() {
            if (!window.APIInterceptor) return;
            
            const rule = {
                name: "JSONPlaceholder测试规则",
                match: "jsonplaceholder.typicode.com",
                enabled: true,
                method: "ALL",
                modify: (data, url, method) => {
                    data.intercepted = true;
                    data.interceptTime = new Date().toISOString();
                    data.originalUrl = url;
                    data.method = method;
                    data.note = "这个数据被API拦截器修改了！";
                    return data;
                }
            };
            
            if (window.APIInterceptor.addRule(rule)) {
                showMessage('success', 'JSONPlaceholder测试规则添加成功');
                checkInterceptor();
            } else {
                showMessage('error', '规则添加失败');
            }
        }

        // 添加本地API测试规则
        function addLocalTestRule() {
            if (!window.APIInterceptor) return;
            
            const rule = {
                name: "本地学生API测试",
                match: "xueshengguanli/student/list",
                enabled: true,
                method: "ALL",
                modify: (data, url, method) => {
                    data.testIntercepted = true;
                    data.testTime = new Date().toISOString();
                    data.testNote = "本地API拦截测试成功！";
                    
                    // 如果有学生列表，为每个学生添加测试标记
                    if (data.list && Array.isArray(data.list)) {
                        data.list.forEach(student => {
                            student.testModified = true;
                        });
                    }
                    
                    return data;
                }
            };
            
            if (window.APIInterceptor.addRule(rule)) {
                showMessage('success', '本地API测试规则添加成功');
                checkInterceptor();
            } else {
                showMessage('error', '规则添加失败');
            }
        }

        // 清除测试规则
        function clearTestRules() {
            if (!window.APIInterceptor) return;
            
            const testRules = ['JSONPlaceholder测试规则', '本地学生API测试'];
            let removed = 0;
            
            testRules.forEach(ruleName => {
                if (window.APIInterceptor.removeRule(ruleName)) {
                    removed++;
                }
            });
            
            showMessage('info', `已清除 ${removed} 个测试规则`);
            checkInterceptor();
        }

        // 测试外部XHR请求
        function testExternalXHR() {
            const resultsDiv = document.getElementById('external-results');
            resultsDiv.innerHTML = '<div class="status info">正在测试外部XHR请求...</div>';
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://jsonplaceholder.typicode.com/posts/1');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        const isIntercepted = data.intercepted === true;
                        
                        resultsDiv.innerHTML = `
                            <div class="status ${isIntercepted ? 'success' : 'info'}">
                                ${isIntercepted ? '✅ XHR请求被成功拦截' : 'ℹ️ XHR请求未被拦截'}
                            </div>
                            <div class="result">${JSON.stringify(data, null, 2)}</div>
                        `;
                    } catch (e) {
                        resultsDiv.innerHTML = `
                            <div class="status error">❌ 响应解析失败</div>
                            <div class="result">${xhr.responseText}</div>
                        `;
                    }
                } else {
                    resultsDiv.innerHTML = `<div class="status error">❌ 请求失败: ${xhr.status}</div>`;
                }
            };
            xhr.onerror = function() {
                resultsDiv.innerHTML = '<div class="status error">❌ 网络错误</div>';
            };
            xhr.send();
        }

        // 测试外部Fetch请求
        function testExternalFetch() {
            const resultsDiv = document.getElementById('external-results');
            resultsDiv.innerHTML = '<div class="status info">正在测试外部Fetch请求...</div>';
            
            fetch('https://jsonplaceholder.typicode.com/posts/2')
                .then(response => response.json())
                .then(data => {
                    const isIntercepted = data.intercepted === true;
                    
                    resultsDiv.innerHTML = `
                        <div class="status ${isIntercepted ? 'success' : 'info'}">
                            ${isIntercepted ? '✅ Fetch请求被成功拦截' : 'ℹ️ Fetch请求未被拦截'}
                        </div>
                        <div class="result">${JSON.stringify(data, null, 2)}</div>
                    `;
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<div class="status error">❌ 请求失败: ${error.message}</div>`;
                });
        }

        // 测试本地API
        function testLocalAPI() {
            const resultsDiv = document.getElementById('local-results');
            resultsDiv.innerHTML = '<div class="status info">正在测试本地学生列表API...</div>';
            
            // 模拟本地API请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '../ngres/xueshengguanli/student/list');
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onload = function() {
                try {
                    const data = JSON.parse(xhr.responseText);
                    const isIntercepted = data.testIntercepted === true;
                    
                    resultsDiv.innerHTML = `
                        <div class="status ${isIntercepted ? 'success' : 'info'}">
                            ${isIntercepted ? '✅ 本地API被成功拦截' : 'ℹ️ 本地API未被拦截'}
                        </div>
                        <div class="result">${JSON.stringify(data, null, 2)}</div>
                    `;
                } catch (e) {
                    resultsDiv.innerHTML = `
                        <div class="status error">❌ 响应解析失败</div>
                        <div class="result">${xhr.responseText}</div>
                    `;
                }
            };
            xhr.onerror = function() {
                resultsDiv.innerHTML = '<div class="status error">❌ 本地API请求失败（可能服务器未启动）</div>';
            };
            
            // 发送测试数据
            xhr.send(JSON.stringify({
                page: { currentPage: 1 },
                searchItems: {}
            }));
        }

        // 测试模拟API
        function testMockAPI() {
            const resultsDiv = document.getElementById('local-results');
            resultsDiv.innerHTML = '<div class="status info">正在测试模拟API...</div>';
            
            // 创建模拟响应
            const mockData = {
                status: "success",
                list: [
                    { id: 1, name: "张三", age: 20 },
                    { id: 2, name: "李四", age: 21 }
                ],
                page: { currentPage: 1, totalPages: 1 }
            };
            
            // 模拟API调用
            setTimeout(() => {
                const modifiedData = window.APIInterceptor ? 
                    JSON.parse(window.APIInterceptor.rules.find(r => r.name === "本地学生API测试" && r.enabled)?.modify(mockData, "xueshengguanli/student/list", "POST") || JSON.stringify(mockData)) :
                    mockData;
                
                const isIntercepted = modifiedData.testIntercepted === true;
                
                resultsDiv.innerHTML = `
                    <div class="status ${isIntercepted ? 'success' : 'info'}">
                        ${isIntercepted ? '✅ 模拟API被成功拦截' : 'ℹ️ 模拟API未被拦截'}
                    </div>
                    <div class="result">${JSON.stringify(modifiedData, null, 2)}</div>
                `;
            }, 500);
        }

        // 显示日志
        function showLogs() {
            if (!window.APIInterceptor) return;
            
            const logs = window.APIInterceptor.getLogs();
            const logsDiv = document.getElementById('logs-display');
            
            if (logs.length === 0) {
                logsDiv.innerHTML = '<div class="status info">暂无拦截日志</div>';
                return;
            }
            
            const logsText = logs.slice(-20).map(log => 
                `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}${log.data ? '\n' + JSON.stringify(log.data, null, 2) : ''}`
            ).join('\n\n');
            
            logsDiv.innerHTML = `<div class="result">${logsText}</div>`;
        }

        // 清空日志
        function clearLogs() {
            if (window.APIInterceptor) {
                window.APIInterceptor.clearLogs();
                showMessage('info', '日志已清空');
                document.getElementById('logs-display').innerHTML = '';
            }
        }

        // 显示消息
        function showMessage(type, message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `status ${type}`;
            messageDiv.textContent = message;
            
            document.body.insertBefore(messageDiv, document.body.firstChild);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // 显示结果
        function showResult(text) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.textContent = text;
            
            document.body.appendChild(resultDiv);
            
            setTimeout(() => {
                resultDiv.remove();
            }, 10000);
        }

        // 页面加载时检查拦截器
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkInterceptor, 100);
        });
    </script>
</body>
</html>
