# 全局API拦截器

## 🎯 项目概述

这是一个强大的全局API拦截器，可以拦截和修改所有HTTP请求的返回值。支持XMLHttpRequest和Fetch API，提供灵活的配置选项和完整的日志记录功能。

## ✨ 主要功能

- **🔄 全面拦截**: 支持XMLHttpRequest和Fetch API
- **⚙️ 灵活配置**: 支持字符串匹配和正则表达式匹配
- **🎛️ 方法过滤**: 可按HTTP方法（GET、POST等）进行过滤
- **✏️ 实时修改**: 可以实时修改API返回数据
- **📊 日志记录**: 完整的拦截日志，支持多种日志级别
- **💾 配置管理**: 支持配置导入导出
- **🖥️ 可视化管理**: 提供Web界面进行管理

## 📁 文件结构

```
src/main/webapp/ng/
├── js/
│   └── filter.js                    # 核心拦截器文件
├── api-interceptor-demo.html        # 可视化管理界面
├── test-interceptor.html           # 测试页面
├── API拦截器使用说明.md             # 详细使用说明
└── README-API拦截器.md             # 本文件
```

## 🚀 快速开始

### 1. 拦截器已自动加载

拦截器已经在 `index.html` 中自动引入：

```html
<script src="js/filter.js" type="text/javascript"></script>
```

### 2. 基本使用

在浏览器控制台中：

```javascript
// 查看拦截器状态
APIInterceptor.config

// 添加拦截规则
APIInterceptor.addRule({
    name: "我的拦截规则",
    match: "/api/users",
    enabled: true,
    method: "GET",
    modify: (data, url, method) => {
        data.intercepted = true;
        return data;
    }
});

// 查看拦截日志
APIInterceptor.getLogs()
```

### 3. 使用管理界面

访问以下页面来使用可视化界面：

- **管理界面**: `api-interceptor-demo.html` - 完整的配置和管理功能
- **测试页面**: `test-interceptor.html` - 测试拦截器功能

## 🔧 核心API

### 全局对象：`window.APIInterceptor`

| 方法 | 描述 | 示例 |
|------|------|------|
| `addRule(rule)` | 添加拦截规则 | `APIInterceptor.addRule({...})` |
| `removeRule(name)` | 移除规则 | `APIInterceptor.removeRule("规则名")` |
| `toggleRule(name, enabled)` | 启用/禁用规则 | `APIInterceptor.toggleRule("规则名", true)` |
| `toggle(enabled)` | 启用/禁用拦截器 | `APIInterceptor.toggle(false)` |
| `getLogs()` | 获取日志 | `APIInterceptor.getLogs()` |
| `clearLogs()` | 清空日志 | `APIInterceptor.clearLogs()` |
| `exportConfig()` | 导出配置 | `APIInterceptor.exportConfig()` |
| `importConfig(config)` | 导入配置 | `APIInterceptor.importConfig(config)` |

## 📋 规则配置

### 基本规则结构

```javascript
{
    name: "规则名称",           // 必填：唯一标识
    match: "匹配模式",          // 必填：URL匹配模式
    enabled: true,             // 可选：是否启用
    method: "ALL",             // 可选：HTTP方法过滤
    modify: (data, url, method) => {  // 必填：修改函数
        // 处理数据
        return data;
    }
}
```

### 匹配模式示例

```javascript
// 字符串匹配
match: "/api/users"

// 正则表达式匹配
match: /\/api\/users\/\d+/

// 包含特定路径
match: "xueshengguanli/student/list"
```

### 修改函数示例

```javascript
modify: (data, url, method) => {
    // 添加字段
    data.intercepted = true;
    data.timestamp = new Date().toISOString();
    
    // 修改数组数据
    if (data.list && Array.isArray(data.list)) {
        data.list.forEach(item => {
            item.modified = true;
        });
    }
    
    // 必须返回修改后的数据
    return data;
}
```

## 🎮 使用场景

### 1. 开发调试

```javascript
// 为所有API响应添加调试信息
APIInterceptor.addRule({
    name: "调试信息",
    match: /\/api\/.*/,
    modify: (data, url, method) => {
        data._debug = {
            url: url,
            method: method,
            timestamp: new Date().toISOString()
        };
        return data;
    }
});
```

### 2. 数据模拟

```javascript
// 模拟用户权限
APIInterceptor.addRule({
    name: "权限模拟",
    match: "/api/userinfo",
    modify: (data, url, method) => {
        data.role = "admin";
        data.permissions = ["read", "write", "delete"];
        return data;
    }
});
```

### 3. 数据增强

```javascript
// 为学生数据添加额外信息
APIInterceptor.addRule({
    name: "学生数据增强",
    match: "student/list",
    modify: (data, url, method) => {
        if (data.list) {
            data.list.forEach(student => {
                student.displayName = `${student.name} (${student.id})`;
                student.isActive = true;
            });
        }
        return data;
    }
});
```

## ⚙️ 配置选项

```javascript
APIInterceptor.config = {
    enabled: true,           // 是否启用拦截器
    logLevel: 'info',       // 日志级别: none, error, warn, info, debug
    logToConsole: true,     // 是否输出到控制台
    logToStorage: false,    // 是否保存到本地存储
    maxLogEntries: 100      // 最大日志条数
}
```

## 🔍 故障排除

### 拦截器未生效

1. 检查拦截器是否启用：`APIInterceptor.config.enabled`
2. 检查规则是否启用：查看 `APIInterceptor.rules`
3. 检查匹配模式是否正确
4. 查看控制台错误信息

### 修改函数不工作

1. 检查函数语法是否正确
2. 确保返回了修改后的数据
3. 检查数据格式是否为JSON
4. 查看拦截日志：`APIInterceptor.getLogs()`

## 📈 性能考虑

- 拦截器对匹配的请求有轻微性能影响
- 建议使用精确的匹配模式减少不必要的处理
- 复杂的修改函数可能影响响应时间
- 可通过禁用不需要的规则来优化性能

## 🔒 安全注意事项

- 拦截器可以修改所有匹配的API响应
- 请谨慎使用，避免破坏应用逻辑
- 生产环境中建议禁用调试相关规则
- 不要在修改函数中执行敏感操作

## 🆕 版本信息

**当前版本**: v2.0.0

### 主要更新

- 重构代码架构，提供更好的API
- 添加可视化管理界面
- 支持配置导入导出
- 增强日志记录功能
- 添加性能监控
- 支持正则表达式匹配
- 改进错误处理

## 📞 技术支持

如果遇到问题或需要帮助：

1. 查看详细使用说明：`API拦截器使用说明.md`
2. 使用测试页面验证功能：`test-interceptor.html`
3. 使用管理界面进行配置：`api-interceptor-demo.html`
4. 查看浏览器控制台的错误信息
5. 检查拦截日志：`APIInterceptor.getLogs()`

---

**注意**: 这个拦截器是为开发和测试目的设计的，请根据实际需求谨慎使用。
