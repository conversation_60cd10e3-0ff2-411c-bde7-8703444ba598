<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试JSON.parse拦截</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试JSON.parse拦截机制</h1>
        <p>这个页面测试新的JSON.parse拦截机制是否能成功修改数据</p>
        
        <button class="button success" onclick="testJSONParse()">🔍 测试JSON.parse拦截</button>
        <button class="button" onclick="testDirectAPI()">📡 测试直接API调用</button>
        <button class="button" onclick="clearResults()">🧹 清空结果</button>
        
        <div id="results"></div>
    </div>

    <!-- 引入拦截器 -->
    <script src="js/filter.js"></script>

    <script>
        function showResult(message, type = 'info', data = null) {
            const resultsDiv = document.getElementById('results');
            const resultHtml = `
                <div class="status ${type}">
                    ${message}
                </div>
                ${data ? `<div class="result">${typeof data === 'string' ? data : JSON.stringify(data, null, 2)}</div>` : ''}
            `;
            resultsDiv.innerHTML += resultHtml;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 测试JSON.parse拦截
        function testJSONParse() {
            clearResults();
            showResult('🧪 开始测试JSON.parse拦截机制...', 'info');

            // 模拟学生列表数据
            const mockStudentData = {
                "page": {
                    "currentPage": 1,
                    "itemsperpage": 10,
                    "totalItems": 100,
                    "totalPages": 10
                },
                "list": [
                    {
                        "id": "test001",
                        "chineseName": "测试学生1",
                        "englishName": "Test Student 1",
                        "age": 15
                    },
                    {
                        "id": "test002",
                        "chineseName": "测试学生2",
                        "englishName": "Test Student 2",
                        "age": 16
                    }
                ]
            };

            const jsonString = JSON.stringify(mockStudentData);
            
            // 模拟调用栈包含student/list
            try {
                // 创建一个包含student/list的函数调用栈
                function simulateStudentListCall() {
                    return JSON.parse(jsonString);
                }
                
                // 重命名函数以包含关键词
                Object.defineProperty(simulateStudentListCall, 'name', {
                    value: 'student/list'
                });
                
                const result = simulateStudentListCall();
                
                // 检查结果
                if (result.intercepted || result.universalIntercepted) {
                    showResult('✅ JSON.parse拦截成功！', 'success');
                    showResult('检测到拦截标记:', 'info', {
                        intercepted: result.intercepted,
                        universalIntercepted: result.universalIntercepted,
                        note: result.note,
                        _debugInfo: result._debugInfo ? '存在' : '不存在'
                    });
                } else {
                    showResult('❌ JSON.parse拦截失败', 'error');
                    showResult('原始数据:', 'info', result);
                }
                
            } catch (e) {
                showResult('❌ 测试过程中出错: ' + e.message, 'error');
            }
        }

        // 测试直接API调用
        function testDirectAPI() {
            clearResults();
            showResult('📡 开始测试直接API调用...', 'info');

            const xhr = new XMLHttpRequest();
            xhr.open('POST', '../ngres/xueshengguanli/student/list');
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onload = function() {
                if (xhr.status === 200) {
                    try {
                        showResult('📥 收到API响应', 'info');
                        
                        // 直接检查responseText
                        const responseText = xhr.responseText;
                        showResult('原始responseText长度: ' + responseText.length, 'info');
                        
                        // 解析数据
                        const data = JSON.parse(responseText);
                        
                        // 检查拦截标记
                        const hasIntercepted = data.intercepted === true;
                        const hasUniversal = data.universalIntercepted === true;
                        const hasDebugInfo = !!data._debugInfo;
                        
                        if (hasIntercepted || hasUniversal || hasDebugInfo) {
                            showResult('🎉 API拦截成功！', 'success');
                            showResult('拦截标记检查:', 'info', {
                                intercepted: hasIntercepted,
                                universalIntercepted: hasUniversal,
                                hasDebugInfo: hasDebugInfo,
                                note: data.note || '无'
                            });
                        } else {
                            showResult('❌ API拦截失败', 'error');
                            showResult('数据结构:', 'info', {
                                hasPage: !!data.page,
                                hasList: !!data.list,
                                listLength: data.list ? data.list.length : 0,
                                topLevelKeys: Object.keys(data)
                            });
                        }
                        
                        // 显示部分数据
                        const dataStr = JSON.stringify(data, null, 2);
                        showResult('API返回数据（前500字符）:', 'info', dataStr.substring(0, 500) + '...');
                        
                    } catch (e) {
                        showResult('❌ 数据解析失败: ' + e.message, 'error');
                        showResult('原始响应:', 'info', xhr.responseText.substring(0, 200) + '...');
                    }
                } else {
                    showResult(`❌ API请求失败: ${xhr.status}`, 'error');
                }
            };

            xhr.onerror = function() {
                showResult('❌ 网络错误', 'error');
            };

            xhr.send(JSON.stringify({
                page: { currentPage: 1, itemsperpage: 10 },
                searchItems: {}
            }));
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            showResult('🚀 页面已加载', 'info');
            showResult('这个页面测试新的JSON.parse拦截机制', 'info');
            
            // 检查拦截器状态
            if (window.APIInterceptor) {
                showResult('✅ 拦截器已加载', 'success');
                const enabledRules = window.APIInterceptor.rules.filter(r => r.enabled);
                showResult(`当前启用规则数: ${enabledRules.length}`, 'info');
            } else {
                showResult('❌ 拦截器未加载', 'error');
            }
        });
    </script>
</body>
</html>
